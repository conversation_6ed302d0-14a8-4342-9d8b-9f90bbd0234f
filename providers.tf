terraform {
  required_version = "~> 1.9" # OpenTofu version

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  # All variables here are set by the IAC pipeline and should not need modifications
  backend "s3" {
    bucket         = var.state_file_root
    key            = "${var.state_file_key}/main.tfstate"
    encrypt        = true
    kms_key_id     = "alias/${var.state_file_root}"
    dynamodb_table = var.state_file_root
  }
}

provider "aws" {
  region = "us-gov-west-1"
  default_tags {
    tags = {
      lifecycle    = local.account_lifecycle
      iac_pipeline = var.repo_name
    }
  }
}