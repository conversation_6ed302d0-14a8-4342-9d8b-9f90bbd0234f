#########################
### orange fortress PAAS nodes
#########################
## all environments get 3 nodes of c5.large with 300gb storage
# non-NNPI fortress EC2 Instances

#########################
### App - WC
### Description - Windchill
#########################

resource "aws_instance" "orange_non_unnpi_app" {
  ami                  = var.orange_non_unnpi_app_instance_ami
  instance_type        = var.orange_non_unnpi_app_instance_type
  monitoring           = true
  ebs_optimized        = true
  tenancy              = "default" #local.account_lifecycle == "prod" ? "dedicated" : "default" #default for orange
  iam_instance_profile = aws_iam_instance_profile.ssm_orange.id

  root_block_device {
    # Root volume configuration. These values are declared in the var.{life_cycles}tfvars file. 
    volume_size = var.orange_non_unnpi_app_os_vol_size
    volume_type = var.orange_non_unnpi_app_os_vol_type
    iops        = var.orange_non_unnpi_app_os_vol_iops
    throughput  = var.orange_non_unnpi_app_os_vol_throughput
    encrypted   = "true"
    kms_key_id  = aws_kms_key.orange_non_unnpi_app.arn
    tags = merge(local.orange_non_unnpi_tags, {
      Name       = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "app", 1, "orange")
      drive_type = "os"
    })
  }

  tags = merge(local.orange_non_unnpi_tags, {
    Name        = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "app", 1, "orange"),
    server_type = "windchill",
    PatchGroup  = "windows-all",
    os          = "windows2019",
    vmRole      = "application",
  })

  network_interface {
    network_interface_id = aws_network_interface.orange_non_unnpi_app.id
    device_index         = 0
  }

  depends_on = [
    aws_network_interface.orange_non_unnpi_app
  ]
  metadata_options {
    instance_metadata_tags = "enabled"
    http_endpoint          = "enabled"
    http_tokens            = "required"
  }
}

# pushed to all azA for orange
resource "aws_network_interface" "orange_non_unnpi_app" {
  subnet_id       = data.aws_ssm_parameter.orange_non_unnpi_app_az_a.value
  private_ips     = [cidrhost(data.aws_subnet.orange_non_unnpi_app_az_a.cidr_block, 8)]
  security_groups = [aws_security_group.orange_non_unnpi_app.id]
  tags = merge(local.orange_non_unnpi_tags, {
    Name = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "app", 1, "orange"),
  })
}


resource "aws_ebs_volume" "orange_non_unnpi_app_1" {
  availability_zone = data.aws_subnet.orange_non_unnpi_app_az_a.availability_zone
  # Data volume configuration. These values are declared in the var.{life_cycles}tfvars file. 
  type       = var.orange_non_unnpi_app_data_vol_1_type
  size       = var.orange_non_unnpi_app_data_vol_1_size
  iops       = var.orange_non_unnpi_app_data_vol_1_iops
  throughput = var.orange_non_unnpi_app_data_vol_1_throughput
  encrypted  = true
  kms_key_id = aws_kms_key.orange_non_unnpi_app.arn
  # snapshot_id       = nonsensitive(lookup(local.orange_snapshot_assignments, "mbps-app${each.key}-orange", null))
  tags = merge(local.orange_non_unnpi_tags, {
    Name       = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "app", 1, "orange"),
    drive_type = "data"
  })
  depends_on = [
    aws_kms_key.orange_non_unnpi_app,
  ]
}

resource "aws_volume_attachment" "orange_non_unnpi_app_1" {
  device_name = "/dev/sdh"
  volume_id   = aws_ebs_volume.orange_non_unnpi_app_1.id
  instance_id = aws_instance.orange_non_unnpi_app.id
  depends_on = [
    aws_ebs_volume.orange_non_unnpi_app_1,
    aws_instance.orange_non_unnpi_app
  ]
}

resource "aws_ebs_volume" "orange_non_unnpi_app_2" {
  availability_zone = data.aws_subnet.orange_non_unnpi_app_az_a.availability_zone
  # Data volume configuration. These values are declared in the var.{life_cycles}tfvars file. 
  type       = var.orange_non_unnpi_app_data_vol_2_type
  size       = var.orange_non_unnpi_app_data_vol_2_size
  iops       = var.orange_non_unnpi_app_data_vol_2_iops
  throughput = var.orange_non_unnpi_app_data_vol_2_throughput
  encrypted  = true
  kms_key_id = aws_kms_key.orange_non_unnpi_app.arn
  # snapshot_id       = nonsensitive(lookup(local.orange_snapshot_assignments, "mbps-app${each.key}-orange", null))
  tags = merge(local.orange_non_unnpi_tags, {
    Name       = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "app", 1, "orange"),
    drive_type = "data"
  })
  depends_on = [
    aws_kms_key.orange_non_unnpi_app,
  ]
}

resource "aws_volume_attachment" "orange_non_unnpi_app_2" {
  device_name = "/dev/sdi"
  volume_id   = aws_ebs_volume.orange_non_unnpi_app_2.id
  instance_id = aws_instance.orange_non_unnpi_app.id
  depends_on = [
    aws_ebs_volume.orange_non_unnpi_app_2,
    aws_instance.orange_non_unnpi_app
  ]
}

resource "aws_kms_key" "orange_non_unnpi_app" {
  description              = "KMS key for application server"
  deletion_window_in_days  = 10
  key_usage                = "ENCRYPT_DECRYPT"
  is_enabled               = true
  enable_key_rotation      = true
  multi_region             = true
  customer_master_key_spec = "SYMMETRIC_DEFAULT"
  policy                   = data.aws_iam_policy_document.general_key_policy.json
  tags = merge(local.orange_non_unnpi_tags, {
    Name = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "app", 1, "orange")
  })
}
