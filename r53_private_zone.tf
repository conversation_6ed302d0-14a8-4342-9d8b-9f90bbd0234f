# Route53 bluewater Zone for ec2 DNS resolution
# This creates a private hosted zone that allows ec2 to ec2 resolution

# Private hosted zone for bluewater domain
resource "aws_route53_zone" "eplm_bluewater" {
  name = "pmdt.mib.bluewater.mil"

  vpc {
    vpc_id = data.aws_ssm_parameter.orange_non_unnpi_vpc.value
  }

  tags = merge(
    local.orange_non_unnpi_tags,
    {
      Name         = "eplm-bluewater"
      Description  = "Private hosted zone for ePLM ec2 bluewater domain resolution"
      iac_pipeline = "true"
    }
  )
}

# A records for each EC2 instance using their private IP addresses
resource "aws_route53_record" "orange_non_unnpi_worker" {
  zone_id = aws_route53_zone.eplm_bluewater.zone_id
  name    = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "worker", 1, "orange")
  type    = "A"
  ttl     = 300
  records = [aws_instance.orange_non_unnpi_worker.private_ip]
}

resource "aws_route53_record" "orange_non_unnpi_app" {
  zone_id = aws_route53_zone.eplm_bluewater.zone_id
  name    = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "app", 1, "orange")
  type    = "A"
  ttl     = 300
  records = [aws_instance.orange_non_unnpi_app.private_ip]
}

resource "aws_route53_record" "orange_non_unnpi_db" {
  zone_id = aws_route53_zone.eplm_bluewater.zone_id
  name    = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "db", 1, "orange")
  type    = "A"
  ttl     = 300
  records = [aws_instance.orange_non_unnpi_db.private_ip]
}

resource "aws_route53_record" "orange_non_unnpi_web" {
  zone_id = aws_route53_zone.eplm_bluewater.zone_id
  name    = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "web", 1, "orange")
  type    = "A"
  ttl     = 300
  records = [aws_instance.orange_non_unnpi_web.private_ip]
}

resource "aws_route53_record" "orange_non_unnpi_wbm" {
  zone_id = aws_route53_zone.eplm_bluewater.zone_id
  name    = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "wbm", 1, "orange")
  type    = "A"
  ttl     = 300
  records = [aws_instance.orange_non_unnpi_wbm.private_ip]
}