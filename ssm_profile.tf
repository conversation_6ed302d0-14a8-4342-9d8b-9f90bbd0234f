## Adding SSM roles and policies
## For role, assume role, and SSM, we need to create a collection of linked resources

## Reference shared_ssm_profile_policy.tf for IAM policy Document Permissions

# Sets the naming scheme for resources in this file.
locals {
  fullname_ssm = "ssm-profile"

  tags_ssm = merge(local.orange_non_unnpi_tags, {
    local-resource-name = local.fullname_ssm
  })
}

resource "aws_iam_role" "ssm_instance" {
  name               = local.fullname_ssm
  description        = "Terraform created Role for using the SSM session manager with an instance for"
  assume_role_policy = data.aws_iam_policy_document.instance_assume_role.json

  tags = local.tags_ssm
}

resource "aws_iam_policy" "ssm_access" {
  name        = local.fullname_ssm
  description = "Terraform created Policy meant to be attached to instances via an instance profile to make ssm work"
  policy      = data.aws_iam_policy_document.ssm_instance_access.json

  tags = local.tags_ssm
}

resource "aws_iam_role_policy_attachment" "ssm_access" {
  role       = aws_iam_role.ssm_instance.name
  policy_arn = aws_iam_policy.ssm_access.arn
}

# attach policy from cloudnative for read only access to the mediatransfer bucket
resource "aws_iam_role_policy_attachment" "ssm_access_s3" {
  role       = aws_iam_role.ssm_instance.name
  policy_arn = data.aws_ssm_parameter.mediatransfer_bucket_policy_ro_arn.value
}

# attach STIG evaluation policy for access to shared resources and results buckets
resource "aws_iam_role_policy_attachment" "ssm_stig_evaluation" {
  role       = aws_iam_role.ssm_instance.name
  policy_arn = aws_iam_policy.stig_evaluation_access.arn
}

resource "aws_iam_instance_profile" "ssm_orange" {
  name = local.fullname_ssm
  role = aws_iam_role.ssm_instance.name

  tags = local.tags_ssm
}
