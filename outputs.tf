# outputs go here
# This file is necessary to pass trivy checks for standard files for terraform
# We do not generally expect any outputs in our projects except for outputs in the ssm_outputs.tf file

# Entra Connect deployment outputs
output "entra_connect_instance_id" {
  description = "EC2 Instance ID for Entra Connect server"
  value       = aws_instance.orange_non_unnpi_entra.id
}

output "entra_connect_private_ip" {
  description = "Private IP address of Entra Connect server"
  value       = aws_instance.orange_non_unnpi_entra.private_ip
}

output "entra_connect_next_steps" {
  description = "Next steps for Entra Connect setup"
  value = <<-EOT

    🎯 ENTRA CONNECT DEPLOYMENT COMPLETE!

    Instance ID: ${aws_instance.orange_non_unnpi_entra.id}
    Private IP:  ${aws_instance.orange_non_unnpi_entra.private_ip}

    📋 NEXT STEPS:
    1. Wait for SSM preparation (~5 minutes)
    2. RDP via AppStream to: ${aws_instance.orange_non_unnpi_entra.private_ip}
    3. Download Entra Connect from Microsoft
    4. Run installation wizard: msiexec /i C:\Installers\AzureADConnect.msi
    5. Configure hybrid identity sync

    🔐 DoD STIG compliant with encrypted storage and proper security groups

    EOT
}
