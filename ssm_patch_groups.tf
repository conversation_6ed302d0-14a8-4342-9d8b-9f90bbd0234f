/*====
Default Patch Groups and Baselines
  We want to err on the side of caution, so we set "default" patch groups for our core OS's
  to "NoPatch" baselines.
======*/

# Windows Default Patch Group for not patching
resource "aws_ssm_default_patch_baseline" "base_no_patch_windows" {
  baseline_id      = aws_ssm_patch_baseline.base_no_patch_windows.id
  operating_system = aws_ssm_patch_baseline.base_no_patch_windows.operating_system
}

resource "aws_ssm_patch_baseline" "base_no_patch_windows" {
  name             = "Windows_All-No-Patching"
  description      = "Reject all patches for Windows."
  operating_system = "WINDOWS"

  approval_rule {
    # Only allow any patches 'on or before' this date
    #   This ensures all patches get excluded
    approve_until_date = "1900-01-01"

    patch_filter {
      key    = "PRODUCT"
      values = ["*"]
    }
  }
}

# RHEL Default Patch Group for not patching
resource "aws_ssm_default_patch_baseline" "base_no_patch_rhel" {
  baseline_id      = aws_ssm_patch_baseline.base_no_patch_rhel.id
  operating_system = aws_ssm_patch_baseline.base_no_patch_rhel.operating_system
}

resource "aws_ssm_patch_baseline" "base_no_patch_rhel" {
  name             = "RHEL_All-No-Patching"
  description      = "Reject all patches for RHEL."
  operating_system = "REDHAT_ENTERPRISE_LINUX"

  approval_rule {
    # Only allow any patches 'on or before' this date
    #   This ensures all patches get excluded
    approve_until_date = "1900-01-01"

    patch_filter {
      key    = "PRODUCT"
      values = ["*"]
    }
  }
}

# Create the "no-patch" Patch Group that both the Windows and RHEL Patch Baselines get applied to
#   Multiple baselines can be attached to a Patch Group, but it has to be with separate Terraform resources
resource "aws_ssm_patch_group" "base_no_patch_windows" {
  baseline_id = aws_ssm_patch_baseline.base_no_patch_windows.id
  patch_group = "no-patching"
}

resource "aws_ssm_patch_group" "base_no_patch_rhel" {
  baseline_id = aws_ssm_patch_baseline.base_no_patch_rhel.id
  patch_group = "no-patching"
}

/*====
Windows System Patch Groups and Baselines
======*/

# Patch group for Windows servers that will get all updates
resource "aws_ssm_patch_group" "base_windows_all_packages" {
  baseline_id = aws_ssm_patch_baseline.base_windows_all_packages.id
  patch_group = "windows-all"
}

resource "aws_ssm_patch_baseline" "base_windows_all_packages" {
  name             = "Windows_All-Packages"
  description      = "Windows patch baseline that will install all available updates."
  operating_system = "WINDOWS"

  # We do not want to let KB5044284 ever apply since it will update the instance to Server 2025
  rejected_patches = [
    "KB5044284"
  ]

  # Flag any "CriticalUpdates" as "Critical" for PatchManager compliance
  #   These get marked "Unspecified" per AWS so they are in their own rule
  approval_rule {
    compliance_level = "CRITICAL"

    patch_filter {
      key    = "CLASSIFICATION"
      values = ["CriticalUpdates"]
    }
  }

  # Categorize Updates by Severity for reporting purposes
  approval_rule {
    compliance_level = "CRITICAL"

    patch_filter {
      key    = "MSRC_SEVERITY"
      values = ["Critical"]
    }
  }

  approval_rule {
    compliance_level = "HIGH"

    patch_filter {
      key    = "MSRC_SEVERITY"
      values = ["Important"]
    }
  }

  approval_rule {
    compliance_level = "MEDIUM"

    patch_filter {
      key    = "MSRC_SEVERITY"
      values = ["Moderate"]
    }
  }

  approval_rule {
    compliance_level = "LOW"

    patch_filter {
      key    = "MSRC_SEVERITY"
      values = ["Low"]
    }
  }

  # Classify all Updates (Non-Critical, Non-Security) items as Low
  approval_rule {
    compliance_level = "LOW"

    patch_filter {
      key    = "CLASSIFICATION"
      values = ["Updates"]
    }
  }

  # Classify everything else as Informational
  approval_rule {
    compliance_level = "INFORMATIONAL"

    patch_filter {
      key    = "PRODUCT"
      values = ["*"]
    }
  }
}

/*====
Linux System Patch Groups and Baselines
======*/

# Patch group for RHEL servers that may have compatibility issues
#   with Java, Kernel, or Redhat-Release package upgrades
resource "aws_ssm_patch_group" "base_rhel_no_sensitive_packages" {
  baseline_id = aws_ssm_patch_baseline.base_rhel_no_sensitive_packages.id
  patch_group = "rhel-no-sensitive"
}

resource "aws_ssm_patch_baseline" "base_rhel_no_sensitive_packages" {
  name             = "RHEL_All-No-Sensitive"
  description      = "RHEL patch baseline that ignores Java, Kernel, and RedHat-Release packages"
  operating_system = "REDHAT_ENTERPRISE_LINUX"

  # We do not want to auto update Java, the Kernel or Redhat-release due to potential compatibility issues
  rejected_patches = [
    "kernel*",
    "java*",
    "redhat-release*"
  ]

  # Categorize Updates by Severity for reporting purposes
  approval_rule {
    compliance_level = "CRITICAL"

    patch_filter {
      key    = "SEVERITY"
      values = ["Critical"]
    }
  }

  approval_rule {
    compliance_level = "HIGH"

    patch_filter {
      key    = "SEVERITY"
      values = ["Important"]
    }
  }

  approval_rule {
    compliance_level = "MEDIUM"

    patch_filter {
      key    = "SEVERITY"
      values = ["Moderate"]
    }
  }

  approval_rule {
    compliance_level = "LOW"

    patch_filter {
      key    = "SEVERITY"
      values = ["Low"]
    }
  }

  # Classify all Bugfix items as Low
  approval_rule {
    compliance_level = "LOW"

    patch_filter {
      key    = "CLASSIFICATION"
      values = ["Bugfix"]
    }
  }

  # Classify everything else as Informational
  approval_rule {
    compliance_level    = "INFORMATIONAL"
    enable_non_security = true

    patch_filter {
      key    = "PRODUCT"
      values = ["*"]
    }
  }
}

# Patch group for RHEL servers that will upgrade all packages
resource "aws_ssm_patch_group" "base_rhel_all_packages" {
  baseline_id = aws_ssm_patch_baseline.base_rhel_all_packages.id
  patch_group = "rhel-all"
}

resource "aws_ssm_patch_baseline" "base_rhel_all_packages" {
  name             = "RHEL_All-Packages"
  description      = "RHEL patch baseline that will install all available updates."
  operating_system = "REDHAT_ENTERPRISE_LINUX"

  rejected_patches = []

  # Categorize Updates by Severity for reporting purposes
  approval_rule {
    compliance_level = "CRITICAL"

    patch_filter {
      key    = "SEVERITY"
      values = ["Critical"]
    }
  }

  approval_rule {
    compliance_level = "HIGH"

    patch_filter {
      key    = "SEVERITY"
      values = ["Important"]
    }
  }

  approval_rule {
    compliance_level = "MEDIUM"

    patch_filter {
      key    = "SEVERITY"
      values = ["Moderate"]
    }
  }

  approval_rule {
    compliance_level = "LOW"

    patch_filter {
      key    = "SEVERITY"
      values = ["Low"]
    }
  }

  # Classify all Bugfix items as Low
  approval_rule {
    compliance_level = "LOW"

    patch_filter {
      key    = "CLASSIFICATION"
      values = ["Bugfix"]
    }
  }

  # Classify everything else as Informational
  approval_rule {
    compliance_level    = "INFORMATIONAL"
    enable_non_security = true

    patch_filter {
      key    = "PRODUCT"
      values = ["*"]
    }
  }
}