resource "aws_security_group" "orange_non_unnpi_wbm" {
  name        = "orange-non-unnpi-${local.account_lifecycle}-wbm"
  description = "Security group for WBM server"
  vpc_id      = data.aws_ssm_parameter.orange_non_unnpi_vpc.value
  tags = merge(local.orange_non_unnpi_tags, {
    Name = "orange-non-unnpi-${local.account_lifecycle}-wbm"
  })
}

resource "aws_vpc_security_group_egress_rule" "orange_non_unnpi_wbm" {
  description       = "Allow all outbound"
  security_group_id = aws_security_group.orange_non_unnpi_wbm.id
  cidr_ipv4         = "0.0.0.0/0"
  ip_protocol       = "-1"
}

# Allow RDP access from app server for management
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_wbm_3389" {
  description                  = "Allow RDP from app server"
  security_group_id            = aws_security_group.orange_non_unnpi_wbm.id
  referenced_security_group_id = aws_security_group.orange_non_unnpi_app.id
  from_port                    = 3389
  to_port                      = 3389
  ip_protocol                  = "tcp"
}

# Allow SQL Server access to database
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_wbm_1433" {
  description                  = "SQL Server ingress traffic from database server"
  security_group_id            = aws_security_group.orange_non_unnpi_wbm.id
  referenced_security_group_id = aws_security_group.orange_non_unnpi_db.id
  from_port                    = 1433
  to_port                      = 1433
  ip_protocol                  = "tcp"
}

# Allow access from app server for integration
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_wbm_app" {
  description                  = "Allow traffic from app server"
  security_group_id            = aws_security_group.orange_non_unnpi_wbm.id
  referenced_security_group_id = aws_security_group.orange_non_unnpi_app.id
  ip_protocol                  = "-1"
}

# Allow access from AppStream for management
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_wbm_appstream_3389" {
  description                  = "Allow RDP from EPLM AppStream NetManaged SG"
  security_group_id            = aws_security_group.orange_non_unnpi_wbm.id
  referenced_security_group_id = data.aws_ssm_parameter.eplm_mgmt_appstream_netmanaged_sg_id.value
  from_port                    = 3389
  to_port                      = 3389
  ip_protocol                  = "tcp"
}

# Add a rule to the database security group to allow SQL Server connections from the WBM server
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_db_wbm" {
  description                  = "SQL Server ingress traffic from WBM server"
  security_group_id            = aws_security_group.orange_non_unnpi_db.id
  referenced_security_group_id = aws_security_group.orange_non_unnpi_wbm.id
  from_port                    = 1433
  to_port                      = 1433
  ip_protocol                  = "tcp"
}
