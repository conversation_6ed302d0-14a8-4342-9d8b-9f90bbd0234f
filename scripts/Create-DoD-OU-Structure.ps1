# Create DoD Compliant OU Structure for ibche.bluewater.local
# This script creates a comprehensive OU structure for Entra Connect sync

# Import AD module
Import-Module ActiveDirectory -Force

# Get domain DN - should be DC=ibche,DC=bluewater,DC=local
$domainDN = (Get-ADDomain).DistinguishedName
Write-Host "Creating DoD compliant OU structure for domain: $domainDN" -ForegroundColor Green

# Create base EntraSync OU
if (-not (Get-ADOrganizationalUnit -LDAPFilter '(ou=EntraSync)' -SearchBase $domainDN -ErrorAction SilentlyContinue)) {
    New-ADOrganizationalUnit -Name 'EntraSync' -Path $domainDN -ProtectedFromAccidentalDeletion $true
    Write-Host 'Created EntraSync base OU' -ForegroundColor Yellow
}

# Create main organizational structure
$baseOU = 'OU=EntraSync,' + $domainDN
$mainOUs = @('Users', 'Groups', 'ServiceAccounts', 'Servers', 'Workstations', 'Devices')
foreach ($ou in $mainOUs) {
    if (-not (Get-ADOrganizationalUnit -LDAPFilter "(ou=$ou)" -SearchBase $baseOU -ErrorAction SilentlyContinue)) {
        New-ADOrganizationalUnit -Name $ou -Path $baseOU -ProtectedFromAccidentalDeletion $true
        Write-Host "Created $ou OU" -ForegroundColor Yellow
    }
}

# Create DoD compliant user sub-OUs
$usersOU = 'OU=Users,' + $baseOU
$userSubOUs = @('Administrators', 'StandardUsers', 'ePLM-Users', 'Contractors', 'ServiceUsers')
foreach ($subOU in $userSubOUs) {
    if (-not (Get-ADOrganizationalUnit -LDAPFilter "(ou=$subOU)" -SearchBase $usersOU -ErrorAction SilentlyContinue)) {
        New-ADOrganizationalUnit -Name $subOU -Path $usersOU -ProtectedFromAccidentalDeletion $true
        Write-Host "Created Users\$subOU OU" -ForegroundColor Cyan
    }
}

# Create group sub-OUs
$groupsOU = 'OU=Groups,' + $baseOU
$groupSubOUs = @('Security-Groups', 'Distribution-Groups', 'Role-Based-Groups', 'Application-Groups')
foreach ($subOU in $groupSubOUs) {
    if (-not (Get-ADOrganizationalUnit -LDAPFilter "(ou=$subOU)" -SearchBase $groupsOU -ErrorAction SilentlyContinue)) {
        New-ADOrganizationalUnit -Name $subOU -Path $groupsOU -ProtectedFromAccidentalDeletion $true
        Write-Host "Created Groups\$subOU OU" -ForegroundColor Cyan
    }
}

# Create server sub-OUs (including where Entra Connect server will be placed)
$serversOU = 'OU=Servers,' + $baseOU
$serverSubOUs = @('DomainControllers', 'MemberServers', 'ApplicationServers', 'DatabaseServers', 'IdentityServers')
foreach ($subOU in $serverSubOUs) {
    if (-not (Get-ADOrganizationalUnit -LDAPFilter "(ou=$subOU)" -SearchBase $serversOU -ErrorAction SilentlyContinue)) {
        New-ADOrganizationalUnit -Name $subOU -Path $serversOU -ProtectedFromAccidentalDeletion $true
        Write-Host "Created Servers\$subOU OU" -ForegroundColor Cyan
    }
}

# Create workstation sub-OUs
$workstationsOU = 'OU=Workstations,' + $baseOU
$workstationSubOUs = @('Standard-Workstations', 'Privileged-Workstations', 'Kiosks', 'FIPS-Compliant-Workstations')
foreach ($subOU in $workstationSubOUs) {
    if (-not (Get-ADOrganizationalUnit -LDAPFilter "(ou=$subOU)" -SearchBase $workstationsOU -ErrorAction SilentlyContinue)) {
        New-ADOrganizationalUnit -Name $subOU -Path $workstationsOU -ProtectedFromAccidentalDeletion $true
        Write-Host "Created Workstations\$subOU OU" -ForegroundColor Cyan
    }
}

Write-Host "`nDoD compliant OU structure created successfully for ibche.bluewater.local" -ForegroundColor Green
Write-Host "Structure includes:" -ForegroundColor White
Write-Host "- EntraSync (base OU)" -ForegroundColor White
Write-Host "  - Users (Administrators, StandardUsers, ePLM-Users, Contractors, ServiceUsers)" -ForegroundColor Gray
Write-Host "  - Groups (Security-Groups, Distribution-Groups, Role-Based-Groups, Application-Groups)" -ForegroundColor Gray
Write-Host "  - ServiceAccounts" -ForegroundColor Gray
Write-Host "  - Servers (DomainControllers, MemberServers, ApplicationServers, DatabaseServers, IdentityServers)" -ForegroundColor Gray
Write-Host "  - Workstations (Standard, Privileged, Kiosks, FIPS-Compliant)" -ForegroundColor Gray
Write-Host "  - Devices" -ForegroundColor Gray
Write-Host "`nReady for Entra Connect sync configuration" -ForegroundColor Green

# Display the target OU for Entra Connect server
Write-Host "`nEntra Connect server should be placed in:" -ForegroundColor Yellow
Write-Host "OU=Servers,OU=EntraSync,DC=ibche,DC=bluewater,DC=local" -ForegroundColor White
