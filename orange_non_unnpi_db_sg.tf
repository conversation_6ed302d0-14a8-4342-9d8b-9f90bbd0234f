resource "aws_security_group" "orange_non_unnpi_db" {
  name        = "orange-non-unnpi-${local.account_lifecycle}-db"
  description = "Security group for Windows Server 2019 SQL Server database"
  vpc_id      = data.aws_ssm_parameter.orange_non_unnpi_vpc.value
  tags = merge(local.orange_non_unnpi_tags, {
    Name = "orange-non-unnpi-${local.account_lifecycle}-db"
  })
}

resource "aws_vpc_security_group_egress_rule" "orange_non_unnpi_db" {
  description       = "Allow all outbound"
  security_group_id = aws_security_group.orange_non_unnpi_db.id
  cidr_ipv4         = "0.0.0.0/0"
  ip_protocol       = "-1"
}

# Allow SQL Server port from app servers
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_db_1433" {
  description                  = "SQL Server egress traffic from app servers"
  security_group_id            = aws_security_group.orange_non_unnpi_db.id
  referenced_security_group_id = aws_security_group.orange_non_unnpi_app.id
  from_port                    = 1433
  to_port                      = 1433
  ip_protocol                  = "tcp"
}

# allow incoming traffic from the "eplm_mgmt_appstream_netmanaged_sg_id" for 1433
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_db_appstream" {
  description                  = "Allow incoming traffic from EPLM AppStream NetManaged SG"
  security_group_id            = aws_security_group.orange_non_unnpi_db.id
  referenced_security_group_id = data.aws_ssm_parameter.eplm_mgmt_appstream_netmanaged_sg_id.value
  from_port                    = 1433
  to_port                      = 1433
  ip_protocol                  = "tcp"
}

# allow incoming traffic from the "eplm_mgmt_appstream_netmanaged_sg_id" for 3389
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_db_rdp_appstream" {
  description                  = "Allow RDP from EPLM AppStream NetManaged SG"
  security_group_id            = aws_security_group.orange_non_unnpi_db.id
  referenced_security_group_id = data.aws_ssm_parameter.eplm_mgmt_appstream_netmanaged_sg_id.value
  from_port                    = 3389
  to_port                      = 3389
  ip_protocol                  = "tcp"
}

# The below were not in the diagram
# # Allow RDP access from app subnet for management
# resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_db_3389" {
#   description       = "Allow RDP from app subnet"
#   security_group_id = aws_security_group.orange_non_unnpi_db.id
#   cidr_ipv4         = data.aws_subnet.orange_non_unnpi_app_az_a.cidr_block
#   from_port         = 3389
#   to_port           = 3389
#   ip_protocol       = "tcp"
# }

# # Allow WinRM access from app subnet for management
# resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_db_5985" {
#   description       = "Allow WinRM from app subnet"
#   security_group_id = aws_security_group.orange_non_unnpi_db.id
#   cidr_ipv4         = data.aws_subnet.orange_non_unnpi_app_az_a.cidr_block
#   from_port         = 5985
#   to_port           = 5986
#   ip_protocol       = "tcp"
# }
