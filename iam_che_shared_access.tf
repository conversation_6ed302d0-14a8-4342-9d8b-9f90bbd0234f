# IAM Resources for access to CHE Core shared resources

# This includes the bucket with EvaluateSTIG code and answer files
# and access to upload results to the IB CHE shared infrastructure buckets

# Policy grants read access to shared CHE bucket
data "aws_iam_policy_document" "stig_evaluation_access" {
  # Allow read access to the shared resources bucket for STIG scripts and answer files
  statement {
    sid    = "STIGSharedResourcesRead"
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:ListBucket"
    ]
    resources = [
      "arn:${data.aws_partition.current.partition}:s3:::${data.aws_ssm_parameter.stig_software_bucket.value}",
      "arn:${data.aws_partition.current.partition}:s3:::${data.aws_ssm_parameter.stig_software_bucket.value}/*"
    ]
    condition {
      test     = "StringEquals"
      variable = "aws:PrincipalOrgID"
      values   = [data.aws_organizations_organization.current.id]
    }
  }

  # Allow write-only access to STIG results bucket (no ListBucket for security)
  statement {
    sid    = "STIGResultsWriteOnly"
    effect = "Allow"
    actions = [
      "s3:PutObject",
      "s3:PutObjectAcl"
    ]
    resources = [
      "arn:${data.aws_partition.current.partition}:s3:::${data.aws_ssm_parameter.stig_results_bucket.value}/*"
    ]
    condition {
      test     = "StringEquals"
      variable = "aws:PrincipalOrgID"
      values   = [data.aws_organizations_organization.current.id]
    }
  }

  # Allow KMS access for decrypting shared resources and encrypting results
  # using keys shared from CHE Core
  statement {
    sid    = "STIGKMSAccess"
    effect = "Allow"
    actions = [
      "kms:Decrypt",
      "kms:GenerateDataKey",
      "kms:DescribeKey"
    ]
    resources = [
      provider::aws::arn_build(
        data.aws_partition.current.partition,
        "kms",
        data.aws_region.current.name,
        data.aws_ssm_parameter.core_prod_account_id.value,
        "*"
      )
    ]
    condition {
      test     = "StringEquals"
      variable = "aws:PrincipalOrgID"
      values   = [data.aws_organizations_organization.current.id]
    }
  }

  # # Allow read access to SSM parameters
  # statement {
  #   sid    = "STIGSSMParameterAccess"
  #   effect = "Allow"
  #   actions = [
  #     "ssm:GetParameter",
  #     "ssm:GetParameters"
  #   ]
  #   resources = [
  #     provider::aws::arn_build(
  #       data.aws_partition.current.partition,
  #       "ssm",
  #       data.aws_region.current.name,
  #       data.aws_caller_identity.current.account_id,
  #       "*"
  #     )
  #   ]
  # }

  # Allow CloudWatch Logs access for EvaluateSTIG logging
  statement {
    sid    = "STIGCloudWatchLogs"
    effect = "Allow"
    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
      "logs:DescribeLogStreams",
      "logs:DescribeLogGroups"
    ]
    resources = [
      "arn:${data.aws_partition.current.partition}:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/ec2/stig-evaluation*"
    ]
  }
}

# Create the managed IAM policy
resource "aws_iam_policy" "stig_evaluation_access" {
  name        = "ib-che-stig-evaluation-access"
  description = "Grants EC2 instances access to IB CHE STIG evaluation resources and results storage"
  policy      = data.aws_iam_policy_document.stig_evaluation_access.json

  tags = local.orange_non_unnpi_tags
}