## Adding SSM roles and policies
## For role, assume role, and SSM, we need to create a collection of linked resources

data "aws_iam_policy_document" "instance_assume_role" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["ec2.amazonaws.com"]
    }
  }
}

data "aws_iam_policy_document" "ssm_instance_access" {
  #checkov:skip=CKV_AWS_111:Assumed role has minimal permissions, and maintaining changes would require large amount of admin overhead
  statement {
    actions = [
      "ssm:DescribeAssociation",
      "ssm:GetDeployablePatchSnapshotForInstance",
      "ssm:GetDocument",
      "ssm:DescribeDocument",
      "ssm:GetManifest",
      "ssm:GetParameter",
      "ssm:GetParameters",
      "ssm:ListAssociations",
      "ssm:ListInstanceAssociations",
      "ssm:PutInventory",
      "ssm:PutComplianceItems",
      "ssm:PutConfigurePackageResult",
      "ssm:UpdateAssociationStatus",
      "ssm:UpdateInstanceAssociationStatus",
      "ssm:UpdateInstanceInformation",
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel",
      "ec2messages:AcknowledgeMessage",
      "ec2messages:DeleteMessage",
      "ec2messages:FailMessage",
      "ec2messages:GetEndpoint",
      "ec2messages:GetMessages",
      "ec2messages:SendReply"
    ]
    resources = ["*"]
    effect    = "Allow"
  }

  statement {
    actions   = ["s3:GetEncryptionConfiguration"]
    resources = ["*"]
    effect    = "Allow"
  }
  #these statements allow access to the central script s3 bucket and its kms key
  # statement {
  #   actions   = ["s3:ListBucket"]
  #   resources = ["*"]
  #   effect    = "Allow"
  # }


  # Pulled from NMMES, check to see if we need these declared S3 buckets, and have it's own unique name for ib-che
  # statement {
  #   actions = ["s3:GetObject"]
  #   resources = ["arn:aws-us-gov:s3:::che-shared/*",
  #   "arn:aws-us-gov:s3:::che-shared"]
  #   effect = "Allow"
  # }

  # Pulled from NMMES, check to see if we need these declared S3 buckets, and have it's own unique name for ib-che
  # #che-shared-stigs
  # statement {
  #   actions   = ["s3:PutObject"]
  #   resources = ["arn:aws-us-gov:s3:::che-shared-stigs/*"]
  #   effect    = "Allow"
  # }

  # 2025-05-28 JT: Removing as it accesses/references an external account
  # we grant access to all the keys in the prod account here, but from the prod account we only allow the s3 bucket key so its OK
  # statement {
  #   actions   = ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*", "kms:DescribeKey"]
  #   resources = ["arn:aws-us-gov:kms:us-gov-west-1:${nonsensitive(data.aws_ssm_parameter.coreaccountnum.value)}:key/*"]
  #   effect    = "Allow"
  # }
  statement {
    actions = [
      "cloudwatch:PutMetricData",
      "ec2:DescribeVolumes",
      "ec2:DescribeTags",
      "logs:PutLogEvents",
      "logs:PutRetentionPolicy",
      "logs:DescribeLogStreams",
      "logs:DescribeLogGroups",
      "logs:CreateLogStream",
      "logs:CreateLogGroup",
      "xray:PutTraceSegments",
      "xray:PutTelemetryRecords",
      "xray:GetSamplingRules",
      "xray:GetSamplingTargets",
      "xray:GetSamplingStatisticSummaries"
    ]
    resources = ["*"]
    effect    = "Allow"
  }
  # We use init vars in powershell scripts, additional paths would need to be listed here based on your needs
  statement {
    actions = [
      "ssm:GetParameters",
      "ssm:GetParameter"
    ]
    resources = ["arn:aws-us-gov:ssm:*:*:parameter/AmazonCloudWatch-*"]
    effect    = "Allow"
  }

  # This Allows Intance to Publish SNS topic for Storage Mount Alerts
  # These lines optional, not needed if only windows instances
  # statement {
  #   actions = [
  #     "sns:ListTagsForResource",
  #     "sns:ListSubscriptionsByTopic",
  #     "sns:Publish",
  #     "sns:GetTopicAttributes"
  #   ]
  #   resources = ["arn:aws-us-gov:sns:us-gov-west-1:${data.aws_caller_identity.current.account_id}:stig-${data.aws_caller_identity.current.account_id}"]
  #   effect    = "Allow"
  # }
  # 2025-05-28 JT: Commented out for now until we get a proper target
  # statement {
  #   actions = [
  #     "sts:AssumeRole"
  #   ]
  #   resources = ["arn:aws-us-gov:iam::${nonsensitive(data.aws_ssm_parameter.coreaccountnum.value)}:role/cloudwatch-assume-role"]
  #   effect    = "Allow"
  # }
  # Statement allows SSM Profile to log Session Manager logs to centralized S3 bucket in CHE Core Prod
  # 2025-05-28 JT: Commented out for now until we need it
  # statement {
  #   actions = [
  #     "s3:PutObject",
  #     "s3:PutObjectAcl"
  #   ]
  #   resources = ["arn:aws-us-gov:s3:::che-ssm-logging/*"]
  #   effect    = "Allow"
  # }
  # Permission to Conduct CIS Scans with Inspector
  statement {
    actions = [
      "inspector2:StartCisSession",
      "inspector2:StopCisSession",
      "inspector2:SendCisSessionTelemetry",
      "inspector2:SendCisSessionHealth"
    ]
    resources = ["*"]
    effect    = "Allow"
  }
  statement {
    effect    = "Allow"
    resources = ["*"]

    actions = [
      "ec2messages:AcknowledgeMessage",
      "ec2messages:DeleteMessage",
      "ec2messages:FailMessage",
      "ec2messages:GetEndpoint",
      "ec2messages:GetMessages",
      "ec2messages:SendReply",
    ]
  }
}
