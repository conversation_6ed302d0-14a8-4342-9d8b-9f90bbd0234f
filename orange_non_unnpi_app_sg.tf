resource "aws_security_group" "orange_non_unnpi_app" {
  description = "Orange application server Security Group"
  name        = "orange-non-unnpi-${local.account_lifecycle}-app"
  vpc_id      = data.aws_ssm_parameter.orange_non_unnpi_vpc.value
  tags = merge(local.orange_non_unnpi_tags, {
    Name = "orange-non-unnpi-${local.account_lifecycle}-app"
  })
}

resource "aws_vpc_security_group_egress_rule" "orange_non_unnpi_app" {
  description       = "Allow all outbound"
  security_group_id = aws_security_group.orange_non_unnpi_app.id
  cidr_ipv4         = "0.0.0.0/0"
  ip_protocol       = "-1"
}

resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_app_443_web" {
  description                  = "HTTPS ingress traffic from Web server"
  security_group_id            = aws_security_group.orange_non_unnpi_app.id
  referenced_security_group_id = aws_security_group.orange_non_unnpi_web.id
  from_port                    = 443
  to_port                      = 443
  ip_protocol                  = "tcp"
}

resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_app_443_worker" {
  description                  = "HTTPS ingress traffic from Worker"
  security_group_id            = aws_security_group.orange_non_unnpi_app.id
  referenced_security_group_id = aws_security_group.orange_non_unnpi_worker.id
  from_port                    = 443
  to_port                      = 443
  ip_protocol                  = "tcp"
}

resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_app_601" {
  description                  = "601 ingress traffic from Worker"
  security_group_id            = aws_security_group.orange_non_unnpi_app.id
  referenced_security_group_id = aws_security_group.orange_non_unnpi_worker.id
  from_port                    = 601
  to_port                      = 601
  ip_protocol                  = "tcp"
}

resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_app_5600" {
  description                  = "5600 ingress traffic from Worker"
  security_group_id            = aws_security_group.orange_non_unnpi_app.id
  referenced_security_group_id = aws_security_group.orange_non_unnpi_worker.id
  from_port                    = 5600
  to_port                      = 5600
  ip_protocol                  = "tcp"
}

# allow incoming traffic from the "eplm_mgmt_appstream_netmanaged_sg_id" for 443
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_app_appstream" {
  description                  = "Allow incoming traffic from EPLM AppStream NetManaged SG"
  security_group_id            = aws_security_group.orange_non_unnpi_app.id
  referenced_security_group_id = data.aws_ssm_parameter.eplm_mgmt_appstream_netmanaged_sg_id.value
  from_port                    = 443
  to_port                      = 443
  ip_protocol                  = "tcp"
}

# allow incoming traffic from the "eplm_mgmt_appstream_netmanaged_sg_id" for 601
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_app_appstream_601" {
  description                  = "Allow incoming traffic from EPLM AppStream NetManaged SG for 601"
  security_group_id            = aws_security_group.orange_non_unnpi_app.id
  referenced_security_group_id = data.aws_ssm_parameter.eplm_mgmt_appstream_netmanaged_sg_id.value
  from_port                    = 601
  to_port                      = 601
  ip_protocol                  = "tcp"
}

# allow incoming traffic from the "eplm_mgmt_appstream_netmanaged_sg_id" for 5600
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_app_appstream_5600" {
  description                  = "Allow incoming traffic from EPLM AppStream NetManaged SG for 5600"
  security_group_id            = aws_security_group.orange_non_unnpi_app.id
  referenced_security_group_id = data.aws_ssm_parameter.eplm_mgmt_appstream_netmanaged_sg_id.value
  from_port                    = 5600
  to_port                      = 5600
  ip_protocol                  = "tcp"
}

# allow incoming traffic from the "eplm_mgmt_appstream_netmanaged_sg_id" for 3389
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_app_appstream_3389" {
  description                  = "Allow incoming traffic from EPLM AppStream NetManaged SG for 3389"
  security_group_id            = aws_security_group.orange_non_unnpi_app.id
  referenced_security_group_id = data.aws_ssm_parameter.eplm_mgmt_appstream_netmanaged_sg_id.value
  from_port                    = 3389
  to_port                      = 3389
  ip_protocol                  = "tcp"
}