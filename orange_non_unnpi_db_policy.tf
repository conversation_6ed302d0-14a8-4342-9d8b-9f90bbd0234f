resource "aws_iam_role" "orange_non_unnpi_db_role" {
  name               = "orange-non-unnpi-${local.account_lifecycle}-db-role"
  description        = "Role for SQL Server EC2 instance to access S3 for database imports/exports"
  assume_role_policy = data.aws_iam_policy_document.orange_non_unnpi_db_assume_role.json
  tags = merge(local.orange_non_unnpi_tags, {
    Name = "orange-non-unnpi-${local.account_lifecycle}-db-role"
  })
}

# Create instance profile for the SQL Server EC2 instance
resource "aws_iam_instance_profile" "orange_non_unnpi_db_profile" {
  name = "orange-non-unnpi-${local.account_lifecycle}-db-profile"
  role = aws_iam_role.orange_non_unnpi_db_role.name

  tags = merge(local.orange_non_unnpi_tags, {
    Name = "orange-non-unnpi-${local.account_lifecycle}-db-profile"
  })
}

# # Attach S3 access policy to role
# resource "aws_iam_role_policy_attachment" "orange_non_unnpi_db_s3_access" {
#   role       = aws_iam_role.orange_non_unnpi_db_role.name
#   policy_arn = aws_iam_policy.orange_non_unnpi_db_s3_access.arn
# }

# Attach SSM policy to the SQL Server role for management capabilities
resource "aws_iam_role_policy_attachment" "orange_non_unnpi_db_ssm_access" {
  role       = aws_iam_role.orange_non_unnpi_db_role.name
  policy_arn = aws_iam_policy.ssm_access.arn
}

# attach policy from cloudnative for read only access to the mediatransfer bucket
resource "aws_iam_role_policy_attachment" "ssm_access_s3_db" {
  role       = aws_iam_role.orange_non_unnpi_db_role.name
  policy_arn = data.aws_ssm_parameter.mediatransfer_bucket_policy_ro_arn.value
}

# attach STIG evaluation policy for access to shared resources and results buckets
resource "aws_iam_role_policy_attachment" "orange_non_unnpi_db_stig_evaluation" {
  role       = aws_iam_role.orange_non_unnpi_db_role.name
  policy_arn = aws_iam_policy.stig_evaluation_access.arn
}

data "aws_iam_policy_document" "orange_non_unnpi_db_assume_role" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["ec2.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

# S3 access policy for SQL Server database imports/exports
# resource "aws_iam_policy" "orange_non_unnpi_db_s3_access" {
#   name        = "orange-non-unnpi-${local.account_lifecycle}-db-s3-access"
#   description = "Policy for SQL Server to access S3 for database imports/exports"
#   policy      = data.aws_iam_policy_document.orange_non_unnpi_db_s3_access.json

#   tags = merge(local.orange_non_unnpi_tags, {
#     Name = "orange-non-unnpi-${local.account_lifecycle}-db-s3-access"
#   })
# }

# # S3 access policy document
# data "aws_iam_policy_document" "orange_non_unnpi_db_s3_access" {
#   statement {
#     effect = "Allow"
#     actions = [
#       "s3:GetObject",
#       "s3:PutObject",
#       "s3:ListBucket",
#       "s3:DeleteObject"
#     ]
#     # TODO: Update these resources once the S3 bucket for Windchill database exports is created
#     # For now, using a placeholder bucket ARN to allow the code to run
#     resources = [
#       # Replace with actual bucket ARNs once created
#       "arn:aws-us-gov:s3:::*", # This will allow access to all S3 buckets temporarily
#     ]
#   }
# }
