resource "aws_security_group" "orange_non_unnpi_web" {
  description = "Orange web server Security Group"
  name        = "orange-non-unnpi-${local.account_lifecycle}-web"
  vpc_id      = data.aws_ssm_parameter.orange_non_unnpi_vpc.value
  tags = merge(local.orange_non_unnpi_tags, {
    Name = "orange-non-unnpi-${local.account_lifecycle}-web"
  })
}

resource "aws_vpc_security_group_egress_rule" "orange_non_unnpi_web" {
  description       = "Allow all outbound"
  security_group_id = aws_security_group.orange_non_unnpi_web.id
  cidr_ipv4         = "0.0.0.0/0"
  ip_protocol       = "-1"
}

# allow incoming traffic from the "eplm_mgmt_appstream_netmanaged_sg_id" 
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_web_appstream" {
  description                  = "Allow incoming traffic from EPLM AppStream NetManaged SG"
  security_group_id            = aws_security_group.orange_non_unnpi_web.id
  referenced_security_group_id = data.aws_ssm_parameter.eplm_mgmt_appstream_netmanaged_sg_id.value
  from_port                    = 443
  to_port                      = 443
  ip_protocol                  = "tcp"
}

# allow incoming traffic from the "eplm_mgmt_appstream_netmanaged_sg_id" for 3389
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_web_rdp_appstream" {
  description                  = "Allow RDP from EPLM AppStream NetManaged SG"
  security_group_id            = aws_security_group.orange_non_unnpi_web.id
  referenced_security_group_id = data.aws_ssm_parameter.eplm_mgmt_appstream_netmanaged_sg_id.value
  from_port                    = 3389
  to_port                      = 3389
  ip_protocol                  = "tcp"
}


## meant to allow web traffic from the nta hosted ALB
# resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_web_443_tga_sub_a" {
#   description       = "HTTPS ingress traffic from TGW subnet A"
#   security_group_id = aws_security_group.orange_non_unnpi_web.id
#   cidr_ipv4         = data.aws_subnet.orange_non_unnpi_tga_az_a.cidr_block
#   from_port         = 443
#   to_port           = 443
#   ip_protocol       = "tcp"
# }

# resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_web_443_tga_sub_b" {
#   description       = "HTTPS ingress traffic from TGW subnet B"
#   security_group_id = aws_security_group.orange_non_unnpi_web.id
#   cidr_ipv4         = data.aws_subnet.orange_non_unnpi_tga_az_b.cidr_block
#   from_port         = 443
#   to_port           = 443
#   ip_protocol       = "tcp"
# }
