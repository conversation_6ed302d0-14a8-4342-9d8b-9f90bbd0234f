locals {
  orange_non_unnpi_tags = {
    nnpi        = "false",
    impactLevel = "il4",
    environment = "zoneB",
    appEnv      = "orange",
    lifecycle   = local.account_lifecycle
  }
  # Determines the value of 'account_lifecycle' based on the input variable.
  # If 'var.account_lifecycle' is set to "main", it assigns "prod" to 'account_lifecycle'.
  # Otherwise, it retains the value of 'var.account_lifecycle'.
  # We always use local.account_lifecycle in the tags.
  account_lifecycle = var.account_lifecycle == "main" ? "prod" : var.account_lifecycle
}
