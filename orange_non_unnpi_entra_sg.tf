resource "aws_security_group" "orange_non_unnpi_entra" {
  description = "Orange Entra Connect server Security Group"
  name        = "orange-non-unnpi-${local.account_lifecycle}-entra"
  vpc_id      = data.aws_ssm_parameter.orange_non_unnpi_vpc.value
  tags = merge(local.orange_non_unnpi_tags, {
    Name = "orange-non-unnpi-${local.account_lifecycle}-entra"
  })
}

# Allow all outbound traffic for Microsoft Entra endpoints
resource "aws_vpc_security_group_egress_rule" "orange_non_unnpi_entra" {
  description       = "Allow all outbound"
  security_group_id = aws_security_group.orange_non_unnpi_entra.id
  cidr_ipv4         = "0.0.0.0/0"
  ip_protocol       = "-1"
}

# Allow RDP access from AppStream for management
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_entra_appstream_3389" {
  description                  = "Allow RDP from EPLM AppStream NetManaged SG"
  security_group_id            = aws_security_group.orange_non_unnpi_entra.id
  referenced_security_group_id = data.aws_ssm_parameter.eplm_mgmt_appstream_netmanaged_sg_id.value
  from_port                    = 3389
  to_port                      = 3389
  ip_protocol                  = "tcp"
}

# Allow HTTPS traffic from other internal systems if needed
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_entra_https_internal" {
  description       = "Allow HTTPS from internal VPC for Entra Connect web interface"
  security_group_id = aws_security_group.orange_non_unnpi_entra.id
  cidr_ipv4         = data.aws_vpc.orange_non_unnpi_vpc.cidr_block
  from_port         = 443
  to_port           = 443
  ip_protocol       = "tcp"
}

# Allow HTTP traffic from internal VPC for initial setup and health checks
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_entra_http_internal" {
  description       = "Allow HTTP from internal VPC for Entra Connect setup"
  security_group_id = aws_security_group.orange_non_unnpi_entra.id
  cidr_ipv4         = data.aws_vpc.orange_non_unnpi_vpc.cidr_block
  from_port         = 80
  to_port           = 80
  ip_protocol       = "tcp"
}

# Allow Entra Connect Health Agent port (if using Azure AD Connect Health)
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_entra_health_agent" {
  description                  = "Allow Entra Connect Health Agent from AppStream"
  security_group_id            = aws_security_group.orange_non_unnpi_entra.id
  referenced_security_group_id = data.aws_ssm_parameter.eplm_mgmt_appstream_netmanaged_sg_id.value
  from_port                    = 9090
  to_port                      = 9090
  ip_protocol                  = "tcp"
}

# Allow communication with AWS Managed Microsoft AD (if needed for domain join)
# Port 389 for LDAP
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_entra_ldap" {
  description       = "Allow LDAP for AWS Managed Microsoft AD communication"
  security_group_id = aws_security_group.orange_non_unnpi_entra.id
  cidr_ipv4         = data.aws_vpc.orange_non_unnpi_vpc.cidr_block
  from_port         = 389
  to_port           = 389
  ip_protocol       = "tcp"
}

# Port 636 for LDAPS (secure LDAP)
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_entra_ldaps" {
  description       = "Allow LDAPS for secure AWS Managed Microsoft AD communication"
  security_group_id = aws_security_group.orange_non_unnpi_entra.id
  cidr_ipv4         = data.aws_vpc.orange_non_unnpi_vpc.cidr_block
  from_port         = 636
  to_port           = 636
  ip_protocol       = "tcp"
}

# Port 88 for Kerberos authentication
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_entra_kerberos" {
  description       = "Allow Kerberos for AWS Managed Microsoft AD authentication"
  security_group_id = aws_security_group.orange_non_unnpi_entra.id
  cidr_ipv4         = data.aws_vpc.orange_non_unnpi_vpc.cidr_block
  from_port         = 88
  to_port           = 88
  ip_protocol       = "tcp"
}

# Port 53 for DNS
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_entra_dns_tcp" {
  description       = "Allow DNS TCP for domain resolution"
  security_group_id = aws_security_group.orange_non_unnpi_entra.id
  cidr_ipv4         = data.aws_vpc.orange_non_unnpi_vpc.cidr_block
  from_port         = 53
  to_port           = 53
  ip_protocol       = "tcp"
}

resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_entra_dns_udp" {
  description       = "Allow DNS UDP for domain resolution"
  security_group_id = aws_security_group.orange_non_unnpi_entra.id
  cidr_ipv4         = data.aws_vpc.orange_non_unnpi_vpc.cidr_block
  from_port         = 53
  to_port           = 53
  ip_protocol       = "udp"
}
