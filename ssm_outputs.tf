# Output
locals {
  # outputs_path = "/tfvars/${var.repo_name}"
}
# Each aws_ssm_parameter resource object needs a brief description of the value stored
#  in the parameter (Type & Datatype refer to settings in AWS, format refers to
#  what Terraform thinks the value is). The general form of an output to SSM Parameter
#  Store should look like:
#
# # Type:string  Datatype:test  Format:string
# resource "aws_ssm_parameter" "name_of_parameter" {
#checkov:skip=CKV2_AWS_34: Non-sensitive information
#   type        = "String"
#   name        = "${local.outputs_path}/name-of-parameter"
#   value       = < the parameter value >
#   description = "Description of the parameter value"
# }

