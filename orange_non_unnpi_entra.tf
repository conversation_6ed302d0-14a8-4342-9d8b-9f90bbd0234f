#########################
### Entra Connect Server
### Description - Microsoft Entra Connect for hybrid identity synchronization
#########################

resource "aws_instance" "orange_non_unnpi_entra" {
  ami                  = var.orange_non_unnpi_entra_instance_ami
  instance_type        = var.orange_non_unnpi_entra_instance_type
  monitoring           = true
  ebs_optimized        = true
  tenancy              = "default" #local.account_lifecycle == "prod" ? "dedicated" : "default"
  iam_instance_profile = aws_iam_instance_profile.ssm_orange.id

  root_block_device {
    # Root volume configuration. These values are declared in the var.{life_cycles}tfvars file. 
    volume_size = var.orange_non_unnpi_entra_os_vol_size
    volume_type = var.orange_non_unnpi_entra_os_vol_type
    iops        = var.orange_non_unnpi_entra_os_vol_iops
    throughput  = var.orange_non_unnpi_entra_os_vol_throughput
    encrypted   = "true"
    kms_key_id  = aws_kms_key.orange_non_unnpi_entra.arn
    tags = merge(local.orange_non_unnpi_tags, {
      Name       = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "entra", 1, "orange")
      drive_type = "os"
    })
  }

  tags = merge(local.orange_non_unnpi_tags, {
    Name        = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "entra", 1, "orange"),
    server_type = "entra-connect",
    PatchGroup  = "windows-all",
    os          = "windows2019",
    vmRole      = "identity-sync",
  })

  network_interface {
    network_interface_id = aws_network_interface.orange_non_unnpi_entra.id
    device_index         = 0
  }

  depends_on = [
    aws_network_interface.orange_non_unnpi_entra
  ]

  metadata_options {
    instance_metadata_tags = "enabled"
    http_endpoint          = "enabled"
    http_tokens            = "required"
  }
}

# Network interface for Entra Connect server
resource "aws_network_interface" "orange_non_unnpi_entra" {
  subnet_id       = data.aws_ssm_parameter.orange_non_unnpi_entra_az_a.value
  private_ips     = [cidrhost(data.aws_subnet.orange_non_unnpi_entra_az_a.cidr_block, 12)]
  security_groups = [aws_security_group.orange_non_unnpi_entra.id]
  tags = merge(local.orange_non_unnpi_tags, {
    Name = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "entra", 1, "orange"),
  })
}

# Data volume for Entra Connect databases and logs
resource "aws_ebs_volume" "orange_non_unnpi_entra_data" {
  availability_zone = data.aws_subnet.orange_non_unnpi_entra_az_a.availability_zone
  # Data volume configuration. These values are declared in the var.{life_cycles}tfvars file. 
  type       = var.orange_non_unnpi_entra_data_vol_type
  size       = var.orange_non_unnpi_entra_data_vol_size
  iops       = var.orange_non_unnpi_entra_data_vol_iops
  throughput = var.orange_non_unnpi_entra_data_vol_throughput
  encrypted  = true
  kms_key_id = aws_kms_key.orange_non_unnpi_entra.arn
  tags = merge(local.orange_non_unnpi_tags, {
    Name       = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "entra", 1, "orange"),
    drive_type = "data"
  })
  depends_on = [
    aws_kms_key.orange_non_unnpi_entra,
  ]
}

# Attach data volume to Entra Connect instance
resource "aws_volume_attachment" "orange_non_unnpi_entra_data" {
  device_name = "/dev/sdh"
  volume_id   = aws_ebs_volume.orange_non_unnpi_entra_data.id
  instance_id = aws_instance.orange_non_unnpi_entra.id
  depends_on = [
    aws_ebs_volume.orange_non_unnpi_entra_data,
    aws_instance.orange_non_unnpi_entra
  ]
}

# KMS key for Entra Connect server encryption
resource "aws_kms_key" "orange_non_unnpi_entra" {
  description              = "KMS key for Entra Connect server"
  deletion_window_in_days  = 10
  key_usage                = "ENCRYPT_DECRYPT"
  is_enabled               = true
  enable_key_rotation      = true
  multi_region             = true
  customer_master_key_spec = "SYMMETRIC_DEFAULT"
  policy                   = data.aws_iam_policy_document.general_key_policy.json
  tags = merge(local.orange_non_unnpi_tags, {
    Name = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "entra", 1, "orange")
  })
}
