#########################
### Entra Connect Server
### Description - Microsoft Entra Connect for hybrid identity synchronization
#########################

resource "aws_instance" "orange_non_unnpi_entra" {
  ami                  = var.orange_non_unnpi_entra_instance_ami
  instance_type        = var.orange_non_unnpi_entra_instance_type
  monitoring           = true
  ebs_optimized        = true
  tenancy              = "default" #local.account_lifecycle == "prod" ? "dedicated" : "default"
  iam_instance_profile = aws_iam_instance_profile.ssm_orange.id

  root_block_device {
    # Root volume configuration. These values are declared in the var.{life_cycles}tfvars file. 
    volume_size = var.orange_non_unnpi_entra_os_vol_size
    volume_type = var.orange_non_unnpi_entra_os_vol_type
    iops        = var.orange_non_unnpi_entra_os_vol_iops
    throughput  = var.orange_non_unnpi_entra_os_vol_throughput
    encrypted   = "true"
    kms_key_id  = aws_kms_key.orange_non_unnpi_entra.arn
    tags = merge(local.orange_non_unnpi_tags, {
      Name       = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "entra", 1, "orange")
      drive_type = "os"
    })
  }

  tags = merge(local.orange_non_unnpi_tags, {
    Name        = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "entra", 1, "orange"),
    server_type = "entra-connect",
    PatchGroup  = "windows-all",
    os          = "windows2019",
    vmRole      = "identity-sync",
  })

  network_interface {
    network_interface_id = aws_network_interface.orange_non_unnpi_entra.id
    device_index         = 0
  }

  depends_on = [
    aws_network_interface.orange_non_unnpi_entra
  ]

  metadata_options {
    instance_metadata_tags = "enabled"
    http_endpoint          = "enabled"
    http_tokens            = "required"
  }
}

# Network interface for Entra Connect server
resource "aws_network_interface" "orange_non_unnpi_entra" {
  subnet_id       = data.aws_ssm_parameter.mgmt_non_unnpi_entra_az_a.value
  private_ips     = [cidrhost(data.aws_subnet.mgmt_non_unnpi_entra_az_a.cidr_block, 20)]
  security_groups = [aws_security_group.orange_non_unnpi_entra.id]
  tags = merge(local.orange_non_unnpi_tags, {
    Name = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "entra", 1, "orange"),
  })
}

# Data volume for Entra Connect databases and logs
resource "aws_ebs_volume" "orange_non_unnpi_entra_data" {
  availability_zone = data.aws_subnet.mgmt_non_unnpi_entra_az_a.availability_zone
  # Data volume configuration. These values are declared in the var.{life_cycles}tfvars file. 
  type       = var.orange_non_unnpi_entra_data_vol_type
  size       = var.orange_non_unnpi_entra_data_vol_size
  iops       = var.orange_non_unnpi_entra_data_vol_iops
  throughput = var.orange_non_unnpi_entra_data_vol_throughput
  encrypted  = true
  kms_key_id = aws_kms_key.orange_non_unnpi_entra.arn
  tags = merge(local.orange_non_unnpi_tags, {
    Name       = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "entra", 1, "orange"),
    drive_type = "data"
  })
  depends_on = [
    aws_kms_key.orange_non_unnpi_entra,
  ]
}

# Attach data volume to Entra Connect instance
resource "aws_volume_attachment" "orange_non_unnpi_entra_data" {
  device_name = "/dev/sdh"
  volume_id   = aws_ebs_volume.orange_non_unnpi_entra_data.id
  instance_id = aws_instance.orange_non_unnpi_entra.id
  depends_on = [
    aws_ebs_volume.orange_non_unnpi_entra_data,
    aws_instance.orange_non_unnpi_entra
  ]
}

# KMS key for Entra Connect server encryption
resource "aws_kms_key" "orange_non_unnpi_entra" {
  description              = "KMS key for Entra Connect server"
  deletion_window_in_days  = 10
  key_usage                = "ENCRYPT_DECRYPT"
  is_enabled               = true
  enable_key_rotation      = true
  multi_region             = true
  customer_master_key_spec = "SYMMETRIC_DEFAULT"
  policy                   = data.aws_iam_policy_document.general_key_policy.json
  tags = merge(local.orange_non_unnpi_tags, {
    Name = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "entra", 1, "orange")
  })
}

# SSM Document for Entra Connect preparation
resource "aws_ssm_document" "entra_connect_prep" {
  name            = "EntraConnect-Preparation-${local.account_lifecycle}"
  document_type   = "Command"
  document_format = "JSON"

  content = jsonencode({
    schemaVersion = "2.2"
    description   = "Prepare Windows Server for Entra Connect installation"
    mainSteps = [{
      action = "aws:runPowerShellScript"
      name   = "PrepareEntraConnect"
      inputs = {
        runCommand = [
          "$ErrorActionPreference = 'Stop'",
          "# Install required Windows features",
          "Install-WindowsFeature RSAT-AD-PowerShell -IncludeAllSubFeature -Verbose",
          "Install-WindowsFeature GPMC -Verbose",
          "# Create installers directory",
          "New-Item -Path 'C:\\Installers' -ItemType Directory -Force | Out-Null",
          "# Configure RDP settings for security",
          "reg add \"HKLM\\SYSTEM\\CurrentControlSet\\Control\\Terminal Server\\WinStations\\RDP-Tcp\" /v UserAuthentication /t REG_DWORD /d 1 /f",
          "# Set TLS 1.2 for secure downloads",
          "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12",
          "Write-Host 'Entra Connect preparation completed successfully'"
        ]
      }
    }]
  })

  tags = merge(local.orange_non_unnpi_tags, {
    Name = "EntraConnect-Preparation-${local.account_lifecycle}"
  })
}

# SSM Association to run preparation on the Entra Connect instance
resource "aws_ssm_association" "entra_connect_prep" {
  name             = aws_ssm_document.entra_connect_prep.name
  association_name = "EntraConnect-Prep-${local.account_lifecycle}"

  targets {
    key    = "InstanceIds"
    values = [aws_instance.orange_non_unnpi_entra.id]
  }

  # Run once after instance creation
  schedule_expression = "rate(1440 minutes)" # Daily, but will only run once due to compliance type
  compliance_severity = "HIGH"
  max_concurrency     = "1"
  max_errors          = "0"

  depends_on = [
    aws_instance.orange_non_unnpi_entra
  ]

  tags = merge(local.orange_non_unnpi_tags, {
    Name = "EntraConnect-Prep-Association-${local.account_lifecycle}"
  })
}

# Optional: Domain join association (only created if directory_id is provided)
resource "aws_ssm_association" "entra_domain_join" {
  count = var.aws_managed_ad_directory_id != null ? 1 : 0

  name             = "AWS-JoinDirectoryServiceDomain"
  association_name = "EntraConnect-DomainJoin-${local.account_lifecycle}"

  targets {
    key    = "InstanceIds"
    values = [aws_instance.orange_non_unnpi_entra.id]
  }

  parameters = {
    directoryId = var.aws_managed_ad_directory_id
    ou          = var.entra_connect_ou_path != null ? var.entra_connect_ou_path : ""
  }

  # Run after the preparation is complete
  depends_on = [
    aws_ssm_association.entra_connect_prep
  ]

  tags = merge(local.orange_non_unnpi_tags, {
    Name = "EntraConnect-DomainJoin-Association-${local.account_lifecycle}"
  })
}

# IAM policy for Entra Connect to access AWS Managed AD and Secrets Manager
resource "aws_iam_policy" "entra_connect_ad_access" {
  name        = "EntraConnect-AD-Access-${local.account_lifecycle}"
  description = "Allow Entra Connect instance to access AWS Managed AD and Secrets Manager"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ds:DescribeDirectories",
          "ds:DescribeTrusts",
          "ds:GetDirectoryLimits"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue",
          "secretsmanager:DescribeSecret"
        ]
        Resource = "arn:aws-us-gov:secretsmanager:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:secret:entra-connect/*"
      }
    ]
  })

  tags = merge(local.orange_non_unnpi_tags, {
    Name = "EntraConnect-AD-Access-${local.account_lifecycle}"
  })
}

# Attach the AD access policy to the SSM role
resource "aws_iam_role_policy_attachment" "entra_connect_ad_access" {
  role       = aws_iam_role.ssm_instance.name
  policy_arn = aws_iam_policy.entra_connect_ad_access.arn
}

# SSM Document for Entra Connect service account setup
resource "aws_ssm_document" "entra_connect_service_account" {
  name            = "EntraConnect-ServiceAccount-${local.account_lifecycle}"
  document_type   = "Command"
  document_format = "JSON"

  content = jsonencode({
    schemaVersion = "2.2"
    description   = "Create and configure Entra Connect service account in AWS Managed AD"
    parameters = {
      ServiceAccountName = {
        type        = "String"
        description = "Name for the Entra Connect service account"
        default     = "EntraConnectSvc"
      }
      ServiceAccountPassword = {
        type        = "String"
        description = "Password for the service account (will be stored in Secrets Manager)"
        default     = "TempPassword123!"
      }
    }
    mainSteps = [{
      action = "aws:runPowerShellScript"
      name   = "CreateServiceAccount"
      inputs = {
        runCommand = [
          "$ErrorActionPreference = 'Stop'",
          "# Import Active Directory module",
          "Import-Module ActiveDirectory -Force",
          "",
          "# Get domain information",
          "$domain = Get-ADDomain",
          "$domainDN = $domain.DistinguishedName",
          "",
          "# Create service account if it doesn't exist",
          "$serviceAccount = '{{ ServiceAccountName }}'",
          "try {",
          "    $existingAccount = Get-ADUser -Identity $serviceAccount -ErrorAction Stop",
          "    Write-Host \"Service account $serviceAccount already exists\"",
          "} catch {",
          "    Write-Host \"Creating service account: $serviceAccount\"",
          "    $securePassword = ConvertTo-SecureString '{{ ServiceAccountPassword }}' -AsPlainText -Force",
          "    New-ADUser -Name $serviceAccount -SamAccountName $serviceAccount -AccountPassword $securePassword -Enabled $true -PasswordNeverExpires $true",
          "}",
          "",
          "# Grant required permissions for Entra Connect",
          "Write-Host 'Granting Entra Connect permissions...'",
          "$accountDN = (Get-ADUser -Identity $serviceAccount).DistinguishedName",
          "",
          "# Grant Replicating Directory Changes permissions",
          "dsacls $domainDN /G \"$($domain.NetBIOSName)\\$serviceAccount`:CA;Replicating Directory Changes\"",
          "dsacls $domainDN /G \"$($domain.NetBIOSName)\\$serviceAccount`:CA;Replicating Directory Changes All\"",
          "",
          "# Grant permissions on Configuration partition",
          "$configDN = (Get-ADRootDSE).configurationNamingContext",
          "dsacls $configDN /G \"$($domain.NetBIOSName)\\$serviceAccount`:CA;Replicating Directory Changes In Filtered Set\"",
          "",
          "Write-Host 'Service account setup completed successfully'",
          "Write-Host \"Domain: $($domain.DNSRoot)\"",
          "Write-Host \"Service Account: $($domain.NetBIOSName)\\$serviceAccount\"",
          "Write-Host \"Next: Configure this account in Entra Connect wizard\""
        ]
      }
    }]
  })

  tags = merge(local.orange_non_unnpi_tags, {
    Name = "EntraConnect-ServiceAccount-${local.account_lifecycle}"
  })
}
