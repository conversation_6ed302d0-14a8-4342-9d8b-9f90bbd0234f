resource "aws_security_group" "orange_non_unnpi_worker" {
  description = "Orange worker instance Security Group"
  name        = "orange-non-unnpi-${local.account_lifecycle}-worker"
  vpc_id      = data.aws_ssm_parameter.orange_non_unnpi_vpc.value
  tags = merge(local.orange_non_unnpi_tags, {
    Name = "orange-non-unnpi-${local.account_lifecycle}-worker"
  })
}

resource "aws_vpc_security_group_egress_rule" "orange_non_unnpi_worker" {
  description       = "Allow all outbound"
  security_group_id = aws_security_group.orange_non_unnpi_worker.id
  cidr_ipv4         = "0.0.0.0/0"
  ip_protocol       = "-1"
}

# allow incoming traffic from the "eplm_mgmt_appstream_netmanaged_sg_id" for 3389
resource "aws_vpc_security_group_ingress_rule" "orange_non_unnpi_worker_rdp_appstream" {
  description                  = "Allow RDP from EPLM AppStream NetManaged SG"
  security_group_id            = aws_security_group.orange_non_unnpi_worker.id
  referenced_security_group_id = data.aws_ssm_parameter.eplm_mgmt_appstream_netmanaged_sg_id.value
  from_port                    = 3389
  to_port                      = 3389
  ip_protocol                  = "tcp"
}