account_lifecycle = "main"
aws_region        = "us-gov-west-1"
repo_name         = "iws-eplm-computestorage"

orange_non_unnpi_web_instance_ami        = "ami-04339404d6ed13e9e" #ib-che-prod-win19-2025-06-11T12-43-19.199Z
orange_non_unnpi_web_instance_type       = "m5.xlarge"             #m5.xlarge
orange_non_unnpi_web_os_vol_type         = "gp3"
orange_non_unnpi_web_os_vol_size         = "128" #128
orange_non_unnpi_web_os_vol_iops         = "3000"
orange_non_unnpi_web_os_vol_throughput   = "125"
orange_non_unnpi_web_data_vol_type       = "gp3"
orange_non_unnpi_web_data_vol_size       = "128"
orange_non_unnpi_web_data_vol_iops       = "3000"
orange_non_unnpi_web_data_vol_throughput = "125"

orange_non_unnpi_app_instance_ami          = "ami-04339404d6ed13e9e" #ib-che-prod-win19-2025-06-11T12-43-19.199Z
orange_non_unnpi_app_instance_type         = "m5.4xlarge"            #m5.4xlarge has 16 vCPU and 64 GiB of RAM
orange_non_unnpi_app_os_vol_type           = "gp3"
orange_non_unnpi_app_os_vol_size           = "128" #128
orange_non_unnpi_app_os_vol_iops           = "3000"
orange_non_unnpi_app_os_vol_throughput     = "125"
orange_non_unnpi_app_data_vol_1_type       = "gp3"
orange_non_unnpi_app_data_vol_1_size       = "256" #256
orange_non_unnpi_app_data_vol_1_iops       = "3000"
orange_non_unnpi_app_data_vol_1_throughput = "125"
orange_non_unnpi_app_data_vol_2_type       = "gp3"
orange_non_unnpi_app_data_vol_2_size       = "4000" #4000
orange_non_unnpi_app_data_vol_2_iops       = "7500" #7500
orange_non_unnpi_app_data_vol_2_throughput = "250"  #250

orange_non_unnpi_worker_instance_ami        = "ami-04339404d6ed13e9e" #ib-che-prod-win19-2025-06-11T12-43-19.199Z
orange_non_unnpi_worker_instance_type       = "m5.xlarge"             #m5.xlarge
orange_non_unnpi_worker_os_vol_type         = "gp3"
orange_non_unnpi_worker_os_vol_size         = "128" #128
orange_non_unnpi_worker_os_vol_iops         = "3000"
orange_non_unnpi_worker_os_vol_throughput   = "125"
orange_non_unnpi_worker_data_vol_type       = "gp3"
orange_non_unnpi_worker_data_vol_size       = "64" #64
orange_non_unnpi_worker_data_vol_iops       = "3000"
orange_non_unnpi_worker_data_vol_throughput = "125"

orange_non_unnpi_db_instance_ami        = "ami-0ebad7aaaaf9e0c16" #ib-che-prod-win19-sql-2025-06-11T12-43-22.777Z
orange_non_unnpi_db_instance_type       = "r5.4xlarge"            #r5.4xlarge has 16 vCPU and 128 GiB RAM
orange_non_unnpi_db_os_vol_type         = "gp3"
orange_non_unnpi_db_os_vol_size         = "128"
orange_non_unnpi_db_os_vol_iops         = "3000"
orange_non_unnpi_db_os_vol_throughput   = "125"
orange_non_unnpi_db_data_vol_type       = "gp3"
orange_non_unnpi_db_data_vol_size       = "256"  #256
orange_non_unnpi_db_data_vol_iops       = "7500" #7500
orange_non_unnpi_db_data_vol_throughput = "250"  #250
orange_non_unnpi_db_logs_vol_type       = "gp3"
orange_non_unnpi_db_logs_vol_size       = "256"
orange_non_unnpi_db_logs_vol_iops       = "3000"
orange_non_unnpi_db_logs_vol_throughput = "125"

# WBM Server
orange_non_unnpi_wbm_instance_ami        = "ami-04339404d6ed13e9e" # Same AMI as other servers
orange_non_unnpi_wbm_instance_type       = "m5.xlarge"             # Full size for production
orange_non_unnpi_wbm_os_vol_size         = 256                     # Consistent sizing
orange_non_unnpi_wbm_os_vol_type         = "gp3"
orange_non_unnpi_wbm_os_vol_iops         = 3000
orange_non_unnpi_wbm_os_vol_throughput   = 125
orange_non_unnpi_wbm_data_vol_size       = 500 # Consistent sizing
orange_non_unnpi_wbm_data_vol_type       = "gp3"
orange_non_unnpi_wbm_data_vol_iops       = 3000
orange_non_unnpi_wbm_data_vol_throughput = 125
