account_lifecycle = "dev"
aws_region        = "us-gov-west-1"
repo_name         = "iws-eplm-computestorage"

orange_non_unnpi_web_instance_ami        = "ami-0e55fbeb9f1a5cb72" #ib-che-dev-win22-2025-06-10T16-10-54.606Z
orange_non_unnpi_web_instance_type       = "t3.micro"
orange_non_unnpi_web_os_vol_type         = "gp3"
orange_non_unnpi_web_os_vol_size         = "128"
orange_non_unnpi_web_os_vol_iops         = "3000"
orange_non_unnpi_web_os_vol_throughput   = "125"
orange_non_unnpi_web_data_vol_type       = "gp3"
orange_non_unnpi_web_data_vol_size       = "64"
orange_non_unnpi_web_data_vol_iops       = "3000"
orange_non_unnpi_web_data_vol_throughput = "125"

orange_non_unnpi_app_instance_ami          = "ami-0d909a18b5f295414" #ib-che-dev-win19-2025-06-10T16-10-47.999Z
orange_non_unnpi_app_instance_type         = "t3.micro"
orange_non_unnpi_app_os_vol_type           = "gp3"
orange_non_unnpi_app_os_vol_size           = "256"
orange_non_unnpi_app_os_vol_iops           = "3000"
orange_non_unnpi_app_os_vol_throughput     = "256"
orange_non_unnpi_app_data_vol_1_type       = "gp3"
orange_non_unnpi_app_data_vol_1_size       = "64"
orange_non_unnpi_app_data_vol_1_iops       = "3000"
orange_non_unnpi_app_data_vol_1_throughput = "125"
orange_non_unnpi_app_data_vol_2_type       = "gp3"
orange_non_unnpi_app_data_vol_2_size       = "256"
orange_non_unnpi_app_data_vol_2_iops       = "3000"
orange_non_unnpi_app_data_vol_2_throughput = "125"

orange_non_unnpi_worker_instance_ami        = "ami-0d909a18b5f295414" #ib-che-dev-win19-2025-06-10T16-10-47.999Z
orange_non_unnpi_worker_instance_type       = "t3.micro"
orange_non_unnpi_worker_os_vol_type         = "gp3"
orange_non_unnpi_worker_os_vol_size         = "128"
orange_non_unnpi_worker_os_vol_iops         = "3000"
orange_non_unnpi_worker_os_vol_throughput   = "125"
orange_non_unnpi_worker_data_vol_type       = "gp3"
orange_non_unnpi_worker_data_vol_size       = "64"
orange_non_unnpi_worker_data_vol_iops       = "3000"
orange_non_unnpi_worker_data_vol_throughput = "125"

orange_non_unnpi_db_instance_ami        = "ami-09390862c3c351ee0" #ib-che-dev-win19-sql-2025-06-10T17-25-50.445Z
orange_non_unnpi_db_instance_type       = "m5.large"
orange_non_unnpi_db_os_vol_type         = "gp3"
orange_non_unnpi_db_os_vol_size         = "128"
orange_non_unnpi_db_os_vol_iops         = "3000"
orange_non_unnpi_db_os_vol_throughput   = "125"
orange_non_unnpi_db_data_vol_type       = "gp3"
orange_non_unnpi_db_data_vol_size       = "64"
orange_non_unnpi_db_data_vol_iops       = "3000"
orange_non_unnpi_db_data_vol_throughput = "125"
orange_non_unnpi_db_logs_vol_type       = "gp3"
orange_non_unnpi_db_logs_vol_size       = "1"
orange_non_unnpi_db_logs_vol_iops       = "3000"
orange_non_unnpi_db_logs_vol_throughput = "125"

# WBM Server
orange_non_unnpi_wbm_instance_ami        = "ami-0d909a18b5f295414" # Same AMI as app servers
orange_non_unnpi_wbm_instance_type       = "t3.micro"              # Cost-optimized for dev
orange_non_unnpi_wbm_os_vol_size         = 128                     # Minimal size for cost savings
orange_non_unnpi_wbm_os_vol_type         = "gp3"
orange_non_unnpi_wbm_os_vol_iops         = 3000
orange_non_unnpi_wbm_os_vol_throughput   = 125
orange_non_unnpi_wbm_data_vol_size       = 64 # Minimal size for cost savings
orange_non_unnpi_wbm_data_vol_type       = "gp3"
orange_non_unnpi_wbm_data_vol_iops       = 3000
orange_non_unnpi_wbm_data_vol_throughput = 125

# Entra Connect Server (deployed to mgmt-non-unnpi-vpc)
orange_non_unnpi_entra_instance_ami        = "ami-0d909a18b5f295414" # Windows Server 2019 for Entra Connect
orange_non_unnpi_entra_instance_type       = "t3.small"              # Adequate for Entra Connect sync operations
orange_non_unnpi_entra_os_vol_type         = "gp3"
orange_non_unnpi_entra_os_vol_size         = "256" # Sufficient for OS, Entra Connect, and logs
orange_non_unnpi_entra_os_vol_iops         = "3000"
orange_non_unnpi_entra_os_vol_throughput   = "256"
orange_non_unnpi_entra_data_vol_type       = "gp3"
orange_non_unnpi_entra_data_vol_size       = "128" # For sync databases and additional storage
orange_non_unnpi_entra_data_vol_iops       = "3000"
orange_non_unnpi_entra_data_vol_throughput = "125"

# Optional: AWS Managed Microsoft AD configuration (uncomment and configure when ready)
# aws_managed_ad_directory_id = "d-xxxxxxxxxx"  # Your AWS Managed Microsoft AD Directory ID
# aws_managed_ad_domain_name  = "corp.example.mil"  # Your domain name
# entra_connect_ou_path       = "OU=Servers,OU=DoD_Compliant,DC=corp,DC=example,DC=mil"  # Target OU for domain join
