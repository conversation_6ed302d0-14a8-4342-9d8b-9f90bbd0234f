name: OpenTofu Pull Request Plan

on:
  pull_request:
    branches:
      - main
      - test
      - dev

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

jobs:      
  TFDeployment:
    name: OpenTofu Pull Request Plan
    permissions:
      contents: read
      id-token: write
    uses: navsea-ib-che/github-workflows-shared/.github/workflows/tf_pull_request_plan.yaml@main
    secrets: inherit
    with:
      AWS_GOV_ODIC_REGION: us-gov-west-1
      TF_VERSION: 1.9.0
      TF_TIMEOUT: 60