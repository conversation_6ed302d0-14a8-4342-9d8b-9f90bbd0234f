name: OpenTofu Linting & Validation

on:
  push:
    branches:
      - main
      - test
      - dev

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:      
  TFDeployment:
    name: OpenTofu Linting & Validation
    permissions:
      contents: write
      actions: read
      pull-requests: read
      statuses: write
      security-events: write
    uses: navsea-ib-che/github-workflows-tf-composite/.github/workflows/opentofu_linting_and_validation.yaml@main
    secrets: inherit
    with:
      TF_VERSION: 1.9.0
      TFLINT_EXTRA_ARGS: |
        --var-file=./vars/main.tfvars \
        --var="state_file_root=tf-state-12345" \
        --var="state_file_key=state/repo/org_name/repo_name" \
        --var="account_lifecycle=main"