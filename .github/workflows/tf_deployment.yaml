name: OpenTofu Plan & Apply

on:
  push:
    branches:
      - main
      - test
      - dev

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false

jobs:      
  TFDeployment:
    name: OpenTofu Plan & Apply
    permissions:
      contents: read
      id-token: write
    uses: navsea-ib-che/github-workflows-shared/.github/workflows/tf_commit_build_and_deploy.yaml@main
    secrets: inherit
    with:
      AWS_GOV_ODIC_REGION: us-gov-west-1
      TF_VERSION: 1.9.0
      TF_TIMEOUT: 60