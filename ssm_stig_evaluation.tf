# SSM Association for automated EvaluateSTIG runs
# Based on NMMES CHE STIG evaluation architecture pattern
# Uses 'runner' scripts and static resources provided by a shared bucket in core-cloudnative

# SSM Association for Windows Servers
resource "aws_ssm_association" "windows_stig_evaluation" {
  name             = "AWS-RunRemoteScript"
  association_name = "Windows-STIG-Evaluation-${local.account_lifecycle}"

  # Target Windows instances using tag-based targeting
  # this doesn't appear to support wildcards, and we use an OS tag and not
  # a platform tag today, so, may have to be updated for other Windows Server
  # versions in the future.
  targets {
    key = "tag:os"
    values = [
      "windows2019",
      "windows2022"
    ]
  }

  # Schedule - Daily at 1000 UTC (same as NMMES CHE)
  schedule_expression = "cron(0 10 ? * * *)"

  parameters = {
    sourceType = "S3"
    sourceInfo = jsonencode({
      "path" = format(
        "https://%s.s3.%s.amazonaws.com/evaluate_stig/evaluate_stig_runner_windows.ps1",
        data.aws_ssm_parameter.stig_software_bucket.value,
        data.aws_region.current.name,
      )
    })
    commandLine = join(" ", [
      "powershell -File evaluate_stig_runner_windows.ps1",
      "-A<PERSON>unt<PERSON>ame \"${data.aws_ssm_parameter.system_name.value}\"",
      "-AccountLifecycle \"${local.account_lifecycle}\"",
      "-EsBucket \"${data.aws_ssm_parameter.stig_software_bucket.value}\"",
      "-EsKey \"${data.aws_ssm_parameter.stig_software_path.value}\"",
      "-AfBucket \"${data.aws_ssm_parameter.stig_software_bucket.value}\"",
      "-AfKey \"${data.aws_ssm_parameter.stig_answer_files_path.value}\"",
      "-OutputBucket \"${data.aws_ssm_parameter.stig_results_bucket.value}\""
    ])
  }

  # Execution controls (following NMMES pattern)
  max_concurrency = "15%"
  max_errors      = "3"

  # Output configuration for logging
  output_location {
    s3_bucket_name = data.aws_ssm_parameter.stig_results_bucket.value
    s3_key_prefix  = "ssm-run-command-logs/${data.aws_ssm_parameter.system_name.value}/${local.account_lifecycle}"
    s3_region      = data.aws_region.current.name
  }

  tags = local.orange_non_unnpi_tags
}

# Not yet tested: Linux STIG Evaluation Association 
# The runner script will likely need tweaks and changes
# Todo for later, when we have Linux machines.

# resource "aws_ssm_association" "linux_stig_evaluation" {

#   name             = "AWS-RunRemoteScript"
#   association_name = "Linux-STIG-Evaluation-${local.account_lifecycle}"

#   # Target Linux instances
#   targets {
#     key    = "tag:os"
#     values = ["rhel*", "linux*"]
#   }

#   # Same schedule as Windows
#   schedule_expression = "cron(0 10 ? * * *)"

#   parameters = {
#     sourceType = ["S3"]
#     sourceInfo = [jsonencode({
#       "path" = "https://${data.aws_ssm_parameter.shared_resources_bucket.value}.s3.${data.aws_region.current.name}.amazonaws.com/static_files/evaluate_stig/evaluate_stig_runner_linux.sh"
#     })]
#     commandLine = [format(
#       "/bin/bash evaluate_stig_runner_linux.sh --account-name \"%s\" --account-lifecycle \"%s\" --es-bucket \"%s\" --es-key \"%s\" --af-bucket \"%s\" --af-key \"%s\" --output-bucket \"%s\"",
#       local.system_name,
#       local.account_lifecycle,
#       data.aws_ssm_parameter.shared_resources_bucket.value,
#       "evaluate_stig/software",
#       data.aws_ssm_parameter.shared_resources_bucket.value,
#       "static_files/evaluate_stig/answer_files/basic",
#       data.aws_ssm_parameter.stig_results_bucket.value
#     )]
#     workingDirectory = ["/opt/ssm/"]
#   }

#   max_concurrency = "15%"
#   max_errors      = "3"

#   output_location {
#     s3_bucket_name = data.aws_ssm_parameter.stig_results_bucket.value
#     s3_key_prefix  = "ssm-logs/stig-evaluation/${local.account_lifecycle}"
#     s3_region      = data.aws_region.current.name
#   }

#   tags = local.orange_non_unnpi_tags
# }