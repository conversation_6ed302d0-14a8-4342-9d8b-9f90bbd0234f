#########################
### WBM Server - WC
### Description - WBM
#########################

resource "aws_instance" "orange_non_unnpi_wbm" {
  ami                  = var.orange_non_unnpi_wbm_instance_ami
  instance_type        = var.orange_non_unnpi_wbm_instance_type
  monitoring           = true
  ebs_optimized        = true
  tenancy              = "default" #local.account_lifecycle == "prod" ? "dedicated" : "default"
  iam_instance_profile = aws_iam_instance_profile.ssm_orange.id

  root_block_device {
    volume_size = var.orange_non_unnpi_wbm_os_vol_size
    volume_type = var.orange_non_unnpi_wbm_os_vol_type
    iops        = var.orange_non_unnpi_wbm_os_vol_iops
    throughput  = var.orange_non_unnpi_wbm_os_vol_throughput
    encrypted   = "true"
    kms_key_id  = aws_kms_key.orange_non_unnpi_wbm.arn
    tags = {
      drive_type = "os"
    }
  }

  tags = merge(local.orange_non_unnpi_tags, {
    Name        = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "wbm", 1, "orange"),
    server_type = "windchill-wbm",
    PatchGroup  = "windows-all",
    os          = "windows2019",
    vmRole      = "application",
  })

  network_interface {
    network_interface_id = aws_network_interface.orange_non_unnpi_wbm.id
    device_index         = 0
  }

  depends_on = [
    aws_network_interface.orange_non_unnpi_wbm
  ]
  metadata_options {
    instance_metadata_tags = "enabled"
    http_endpoint          = "enabled"
    http_tokens            = "required"
  }
}

resource "aws_ebs_volume" "orange_non_unnpi_wbm" {
  availability_zone = data.aws_subnet.orange_non_unnpi_app_az_a.availability_zone
  size              = var.orange_non_unnpi_wbm_data_vol_size
  type              = var.orange_non_unnpi_wbm_data_vol_type
  iops              = var.orange_non_unnpi_wbm_data_vol_iops
  throughput        = var.orange_non_unnpi_wbm_data_vol_throughput
  encrypted         = true
  kms_key_id        = aws_kms_key.orange_non_unnpi_wbm.arn
  tags = {
    Name       = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "wbm", 1, "orange"),
    drive_type = "data"
  }
}

resource "aws_volume_attachment" "orange_non_unnpi_wbm" {
  device_name = "/dev/sdh"
  volume_id   = aws_ebs_volume.orange_non_unnpi_wbm.id
  instance_id = aws_instance.orange_non_unnpi_wbm.id
  depends_on = [
    aws_ebs_volume.orange_non_unnpi_wbm,
    aws_instance.orange_non_unnpi_wbm
  ]
}

resource "aws_network_interface" "orange_non_unnpi_wbm" {
  subnet_id       = data.aws_ssm_parameter.orange_non_unnpi_app_az_a.value
  private_ips     = [cidrhost(data.aws_subnet.orange_non_unnpi_app_az_a.cidr_block, 16)]
  security_groups = [aws_security_group.orange_non_unnpi_wbm.id]
  tags = merge(local.orange_non_unnpi_tags, {
    Name = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "wbm", 1, "orange")
  })
}

resource "aws_kms_key" "orange_non_unnpi_wbm" {
  description              = "KMS key for WBM server"
  deletion_window_in_days  = 10
  key_usage                = "ENCRYPT_DECRYPT"
  is_enabled               = true
  enable_key_rotation      = true
  multi_region             = true
  customer_master_key_spec = "SYMMETRIC_DEFAULT"
  policy                   = data.aws_iam_policy_document.general_key_policy.json
  tags = merge(local.orange_non_unnpi_tags, {
    Name = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "wbm", 1, "orange")
  })
}
