################################################################################
# This is used to simplify the SSM Parameter inputs
locals {
  inputs_path         = "/tfvars/${var.repo_name}"
  network_inputs_path = "/tfvars/network"
}

## ALL SSM parameter inputs are defined here, including ones from other repos/parts of a project

### We do additional checks for user-inputted values including:
### 1. Checking for trailing/leading spaces in the SSM parameters (we expect none)
### 2. Outputting them if they are not securestrings so that we can check them in tf plans

## Each aws_ssm_parameter data object needs a brief description of the value stored
##  in the parameter (Type & Datatype refer to settings in AWS, Format refers to
##  what Terraform thinks the value is).
## We do additional checks for user-inputted values including:
##  1. Checking for trailing/leading spaces in the SSM parameters (we expect none)
##  2. Outputting them if they are not securestrings so that we can check them in tfc
## The general format of an input from SSM Parameter Store should look like:

# Core Account ID - for the Production core environment
# EvaluateSTIG in dev, test and prod for this application use the shared
# resources in the Core's Production environment.
data "aws_ssm_parameter" "core_prod_account_id" {
  name = "/tfvars/init/CoreProdAccountId"
}

# My system name - abbreviated name for the system suitable for using in prefixes
data "aws_ssm_parameter" "system_name" {
  name = "/tfvars/init/AppName"
}

################################################################################
# CHE Core Shared Resources - STIG Evaluation Parameters
################################################################################
# These parameters are shared from the Core account, and so need to be
# referenced using a full ARN.

# Type: String, Datatype: String, Format: S3 bucket name
data "aws_ssm_parameter" "stig_software_bucket" {
  name = provider::aws::arn_build(
    data.aws_partition.current.partition,
    "ssm",
    data.aws_region.current.name,
    data.aws_ssm_parameter.core_prod_account_id.value,
    "parameter/shared/che-core/evaluate-stig/bucket"
  )
}

# Type: String, Datatype: String, Format: S3 bucket name
data "aws_ssm_parameter" "stig_results_bucket" {
  name = provider::aws::arn_build(
    data.aws_partition.current.partition,
    "ssm",
    data.aws_region.current.name,
    data.aws_ssm_parameter.core_prod_account_id.value,
    "parameter/shared/che-core/evaluate-stig/results-bucket"
  )
}

# Type: String, Datatype: String, Format: S3 path prefix
data "aws_ssm_parameter" "stig_answer_files_path" {
  name = provider::aws::arn_build(
    data.aws_partition.current.partition,
    "ssm",
    data.aws_region.current.name,
    data.aws_ssm_parameter.core_prod_account_id.value,
    "parameter/shared/che-core/evaluate-stig/answer-files-path"
  )
}

# Type: String, Datatype: String, Format: S3 path prefix
data "aws_ssm_parameter" "stig_software_path" {
  name = provider::aws::arn_build(
    data.aws_partition.current.partition,
    "ssm",
    data.aws_region.current.name,
    data.aws_ssm_parameter.core_prod_account_id.value,
    "parameter/shared/che-core/evaluate-stig/software-path"
  )
}


###############################
##### Networking Resources ####
###############################

data "aws_ssm_parameter" "orange_non_unnpi_vpc" {
  name = "/tfvars/network/orange-non-unnpi-vpc/vpc/orange-non-unnpi-vpc-id"
}

# Create VPC object for security group CIDR references
data "aws_vpc" "orange_non_unnpi_vpc" {
  id = data.aws_ssm_parameter.orange_non_unnpi_vpc.value
}

# Retrieve Web subnet AZ A
data "aws_ssm_parameter" "orange_non_unnpi_web_az_a" {
  name = "/tfvars/network/orange-non-unnpi-vpc/subnets/web1-1a-id"
}

# Create subnet object for Web subnets
data "aws_subnet" "orange_non_unnpi_web_az_a" {
  id = data.aws_ssm_parameter.orange_non_unnpi_web_az_a.value
}

# Retrieve Web subnet AZ B
# data "aws_ssm_parameter" "orange_non_unnpi_web_azB" {
#   name = "/tfvars/network/orange-non-unnpi-vpc/subnets/web2-1b-id"
# }

# data "aws_ssm_parameter" "coreaccountnum" {
#   name = "/tfvars/init/CoreAccountID"
# }

# Retrieve App subnet AZ A
data "aws_ssm_parameter" "orange_non_unnpi_app_az_a" {
  name = "/tfvars/network/orange-non-unnpi-vpc/subnets/app1-1a-id"
}

# Create subnet object for App subnets
data "aws_subnet" "orange_non_unnpi_app_az_a" {
  id = data.aws_ssm_parameter.orange_non_unnpi_app_az_a.value
}

# Retrieve App subnet AZ B
# data "aws_ssm_parameter" "orange_non_unnpi_app_azB" {
#   name = "/tfvars/network/orange-non-unnpi-vpc/subnets/app2-1b-id"
# }

# Retrieve DB subnet AZ A
data "aws_ssm_parameter" "orange_non_unnpi_db_az_a" {
  name = "/tfvars/network/orange-non-unnpi-vpc/subnets/database1-1a-id"
}

# Create subnet object for DB subnet
data "aws_subnet" "orange_non_unnpi_db_az_a" {
  id = data.aws_ssm_parameter.orange_non_unnpi_db_az_a.value
}

#Retrieve Transit Gateway Attachment Subnet A Id
data "aws_ssm_parameter" "orange_non_unnpi_tga_az_a" {
  name = "/tfvars/network/orange-non-unnpi-vpc/subnets/tga1-1a-id"
}

data "aws_subnet" "orange_non_unnpi_tga_az_a" {
  id = data.aws_ssm_parameter.orange_non_unnpi_tga_az_a.value
}

#Retrieve Transit Gateway Attachment Subnet B Id
data "aws_ssm_parameter" "orange_non_unnpi_tga_az_b" {
  name = "/tfvars/network/orange-non-unnpi-vpc/subnets/tga2-1b-id"
}

data "aws_subnet" "orange_non_unnpi_tga_az_b" {
  id = data.aws_ssm_parameter.orange_non_unnpi_tga_az_b.value
}

# Retrieve Entra Connect subnet AZ A (using app subnet like WBM for management services)
data "aws_ssm_parameter" "orange_non_unnpi_entra_az_a" {
  name = "/tfvars/network/orange-non-unnpi-vpc/subnets/app1-1a-id"
}

# Create subnet object for Entra Connect subnet
data "aws_subnet" "orange_non_unnpi_entra_az_a" {
  id = data.aws_ssm_parameter.orange_non_unnpi_entra_az_a.value
}

data "aws_ssm_parameter" "eplm_mgmt_appstream_netmanaged_sg_id" {
  name = "/tfvars/network/mgmt-non-unnpi-vpc/sg/mgmt-non-unnpi-netmanaged_sg_id"
}

# Security group for Windchill Web Server. SG defined in Networking repo,
# to avoid dependency problems with SG to SG references in rules, rules are
# defined in this repo as well as Networking.
# Type:string  Datatype:text  Format:string
data "aws_ssm_parameter" "orange_alb_webserver_access_sg_id" {
  name = "${local.network_inputs_path}/orange-non-unnpi-vpc/sg/orange-alb-webserver-access-id"
  lifecycle {
    postcondition {
      condition     = trimspace(self.value) == self.value
      error_message = "There is a leading or trailing blank space."
    }
  }
}

# ALB target group for testing ALB defined in networking repo
# Type:string  Datatype:text  Format:string
data "aws_ssm_parameter" "eplm_web_target_group_arn" {
  name = "${local.network_inputs_path}/alb/orange-web-target-group-arn"
  lifecycle {
    postcondition {
      condition     = trimspace(self.value) == self.value
      error_message = "There is a leading or trailing blank space."
    }
  }
}

#################################
#### CloudNative Stack Stuff ####
#################################

#### Policy for the media-transfer bucket from cloudnative
data "aws_ssm_parameter" "mediatransfer_bucket_policy_ro_arn" {
  name = "/tfvars/cloudnative/mediatransfer-orange/ro-policy"
}
