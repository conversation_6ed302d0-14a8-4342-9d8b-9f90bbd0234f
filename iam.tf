data "aws_iam_policy_document" "general_key_policy" {
  statement {
    sid    = "generalKeyDeletionPolicy"
    effect = "Allow"
    principals {
      type = "AWS"
      # "root" in this context does not mean "root user", per https://docs.aws.amazon.com/kms/latest/developerguide/key-policy-overview.html#key-policy-elements
      identifiers = ["arn:aws-us-gov:iam::${data.aws_caller_identity.current.account_id}:root"]
    }
    actions = [
      "kms:*"
    ]
    resources = ["*"]
  }
}
