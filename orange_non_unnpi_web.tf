#########################
### Web - Webserver WC
### Description - Apache
#########################

resource "aws_instance" "orange_non_unnpi_web" {
  ami                  = var.orange_non_unnpi_web_instance_ami
  instance_type        = var.orange_non_unnpi_web_instance_type
  monitoring           = true
  ebs_optimized        = true
  tenancy              = "default" #local.account_lifecycle == "prod" ? "dedicated" : "default"
  iam_instance_profile = aws_iam_instance_profile.ssm_orange.id
  root_block_device {
    # Root volume configuration. These values are declared in the var.{life_cycles}tfvars file. 
    volume_size = var.orange_non_unnpi_web_os_vol_size
    volume_type = var.orange_non_unnpi_web_os_vol_type
    iops        = var.orange_non_unnpi_web_os_vol_iops
    throughput  = var.orange_non_unnpi_web_os_vol_throughput
    encrypted   = "true"
    kms_key_id  = aws_kms_key.orange_non_unnpi_web.arn
    tags = merge(local.orange_non_unnpi_tags, {
      Name       = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "web", 1, "orange"),
      drive_type = "os"
    })
  }

  tags = merge(local.orange_non_unnpi_tags, {
    Name        = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "web", 1, "orange"),
    server_type = "proxy"
    PatchGroup  = "windows-all",
    os          = "windows2019",
    vmRole      = "application",
  })

  network_interface {
    network_interface_id = aws_network_interface.orange_non_unnpi_web.id
    device_index         = 0
  }
  depends_on = [
    aws_network_interface.orange_non_unnpi_web
  ]
  metadata_options {
    instance_metadata_tags = "enabled"
    http_endpoint          = "enabled"
    http_tokens            = "required"
  }
}

resource "aws_ebs_volume" "orange_non_unnpi_web" {
  availability_zone = data.aws_subnet.orange_non_unnpi_web_az_a.availability_zone
  # Data volume configuration. These values are declared in the var.{life_cycles}tfvars file. 
  size       = var.orange_non_unnpi_web_data_vol_size
  type       = var.orange_non_unnpi_web_data_vol_type
  iops       = var.orange_non_unnpi_web_data_vol_iops
  throughput = var.orange_non_unnpi_web_data_vol_throughput
  encrypted  = true
  kms_key_id = aws_kms_key.orange_non_unnpi_web.arn
  tags = {
    Name       = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "web", 1, "orange"),
    drive_type = "data"
  }
}

resource "aws_volume_attachment" "orange_non_unnpi_web" {
  device_name = "/dev/sdh"
  volume_id   = aws_ebs_volume.orange_non_unnpi_web.id
  instance_id = aws_instance.orange_non_unnpi_web.id
  depends_on = [
    aws_ebs_volume.orange_non_unnpi_web,
    aws_instance.orange_non_unnpi_web
  ]
}

resource "aws_network_interface" "orange_non_unnpi_web" {
  subnet_id   = data.aws_ssm_parameter.orange_non_unnpi_web_az_a.value
  private_ips = [cidrhost(data.aws_subnet.orange_non_unnpi_web_az_a.cidr_block, 4)]
  security_groups = [
    aws_security_group.orange_non_unnpi_web.id,
    # SG in network stack, allows ALB TCP 443 inbound
    data.aws_ssm_parameter.orange_alb_webserver_access_sg_id.value
  ]
  tags = merge(local.orange_non_unnpi_tags, {
    Name = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "web", 1, "orange")
  })
}

resource "aws_kms_key" "orange_non_unnpi_web" {
  description              = "KMS key for web server"
  deletion_window_in_days  = 10
  key_usage                = "ENCRYPT_DECRYPT"
  is_enabled               = true
  enable_key_rotation      = true
  multi_region             = true
  customer_master_key_spec = "SYMMETRIC_DEFAULT"
  policy                   = data.aws_iam_policy_document.general_key_policy.json
  tags = merge(local.orange_non_unnpi_tags, {
    Name = format("w%.1so-eplm-%.3s%d%.1s", local.account_lifecycle, "web", 1, "orange")
  })
}

# Attach web server to internal testing ALB target group
resource "aws_lb_target_group_attachment" "orange_non_unnpi_web" {
  target_group_arn = data.aws_ssm_parameter.eplm_web_target_group_arn.value
  target_id        = aws_instance.orange_non_unnpi_web.id
  port             = 443
}
