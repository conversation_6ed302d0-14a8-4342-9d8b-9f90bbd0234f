variable "account_lifecycle" {
  type        = string
  description = "dev, test, or main (production). This should be set by the IaC Pipeline"
  validation {
    condition     = contains(["dev", "test", "main"], var.account_lifecycle)
    error_message = "Valid values for variable are: dev, test, main"
  }
}

###############################################################################
# AWS
###############################################################################

variable "aws_region" {
  type        = string
  description = "AWS Region where OpenTofu state is stored."
  default     = "us-gov-west-1"
}


variable "repo_name" {
  type        = string
  description = "Name of the repository."
}

variable "state_file_root" {
  description = "State file path information. This should be set by the IAC Pipeline"
  type        = string
}

variable "state_file_key" {
  description = "State File bucket key. This should be set by the IAC Pipeline"
  type        = string
}

variable "orange_non_unnpi_web_instance_ami" {
  description = "Instance AMI for web server"
  type        = string
}
variable "orange_non_unnpi_web_instance_type" {
  description = "Instance type for web server"
  type        = string
}

variable "orange_non_unnpi_web_os_vol_type" {
  description = "OS EBS volume type for web server"
  type        = string
}
variable "orange_non_unnpi_web_os_vol_size" {
  description = "OS EBS volume size for web server"
  type        = string
}

variable "orange_non_unnpi_web_os_vol_iops" {
  description = "OS EBS volume iops for web server"
  type        = string
}

variable "orange_non_unnpi_web_os_vol_throughput" {
  description = "OS EBS volume throughput for web server"
  type        = string
}

variable "orange_non_unnpi_web_data_vol_type" {
  description = "Data EBS volume type for web server"
  type        = string
}
variable "orange_non_unnpi_web_data_vol_size" {
  description = "Data EBS volume size for web server"
  type        = string
}

variable "orange_non_unnpi_web_data_vol_iops" {
  description = "Data EBS volume iops for web server"
  type        = string
}

variable "orange_non_unnpi_web_data_vol_throughput" {
  description = "Data EBS volume throughput for web server"
  type        = string
}


variable "orange_non_unnpi_app_instance_ami" {
  description = "Instance AMI for app server"
  type        = string
}
variable "orange_non_unnpi_app_instance_type" {
  description = "Instance type for app server"
  type        = string
}

variable "orange_non_unnpi_app_os_vol_type" {
  description = "OS EBS volume type for web server"
  type        = string
}
variable "orange_non_unnpi_app_os_vol_size" {
  description = "OS EBS volume size for web server"
  type        = string
}

variable "orange_non_unnpi_app_os_vol_iops" {
  description = "OS EBS volume iops for web server"
  type        = string
}

variable "orange_non_unnpi_app_os_vol_throughput" {
  description = "OS EBS volume throughput for web server"
  type        = string
}

variable "orange_non_unnpi_app_data_vol_1_type" {
  description = "OS EBS volume type for web server"
  type        = string
}
variable "orange_non_unnpi_app_data_vol_1_size" {
  description = "OS EBS volume size for web server"
  type        = string
}

variable "orange_non_unnpi_app_data_vol_1_iops" {
  description = "OS EBS volume iops for web server"
  type        = string
}

variable "orange_non_unnpi_app_data_vol_1_throughput" {
  description = "OS EBS volume throughput for web server"
  type        = string
}
variable "orange_non_unnpi_app_data_vol_2_type" {
  description = "Data EBS volume type for app server"
  type        = string
}
variable "orange_non_unnpi_app_data_vol_2_size" {
  description = "Data EBS volume size for app server"
  type        = string
}

variable "orange_non_unnpi_app_data_vol_2_iops" {
  description = "Data EBS volume iops for app server"
  type        = string
}

variable "orange_non_unnpi_app_data_vol_2_throughput" {
  description = "Data EBS volume throughput for app server"
  type        = string
}

variable "orange_non_unnpi_worker_instance_ami" {
  type        = string
  description = "Instance AMI for the Creo Worker"
}

variable "orange_non_unnpi_worker_instance_type" {
  type        = string
  description = "Instance type for the Creo Worker"
}

variable "orange_non_unnpi_worker_os_vol_type" {
  type        = string
  description = "OS EBS volume type for the Creo Worker"
}

variable "orange_non_unnpi_worker_os_vol_size" {
  type        = string
  description = "OS EBS volume size for Creo Worker"
}

variable "orange_non_unnpi_worker_os_vol_iops" {
  type        = string
  description = "OS EBS volume IOPS for Creo Worker"
}

variable "orange_non_unnpi_worker_os_vol_throughput" {
  type        = string
  description = "OS EBS volume throughput for Creo Worker"
}

variable "orange_non_unnpi_worker_data_vol_type" {
  type        = string
  description = "Data EBS volume type for Creo Worker"
}

variable "orange_non_unnpi_worker_data_vol_size" {
  type        = string
  description = "Data EBS volume size for Creo Worker"
}

variable "orange_non_unnpi_worker_data_vol_iops" {
  type        = string
  description = "Data EBS volume IOPS for Creo Worker"
}

variable "orange_non_unnpi_worker_data_vol_throughput" {
  type        = string
  description = "Data EBS volume throughput for Creo Worker"
}

variable "orange_non_unnpi_db_instance_ami" {
  type        = string
  description = "Instance AMI for the Database Server"
}

variable "orange_non_unnpi_db_instance_type" {
  type        = string
  description = "Instance type for the Database Server"
}

variable "orange_non_unnpi_db_os_vol_type" {
  type        = string
  description = "OS EBS volume type for the Database Server"
}

variable "orange_non_unnpi_db_os_vol_size" {
  type        = string
  description = "OS EBS volume size for Database Server"
}

variable "orange_non_unnpi_db_os_vol_iops" {
  type        = string
  description = "OS EBS volume IOPS for Database Server"
}

variable "orange_non_unnpi_db_os_vol_throughput" {
  type        = string
  description = "OS EBS volume throughput for Database Server"
}

variable "orange_non_unnpi_db_data_vol_type" {
  type        = string
  description = "Data EBS volume type for Database Server"
}

variable "orange_non_unnpi_db_data_vol_size" {
  type        = string
  description = "Data EBS volume size for Database Server"
}

variable "orange_non_unnpi_db_data_vol_iops" {
  type        = string
  description = "Data EBS volume IOPS for Database Server"
}

variable "orange_non_unnpi_db_data_vol_throughput" {
  type        = string
  description = "Data EBS volume throughput for Database Server"
}

variable "orange_non_unnpi_db_logs_vol_type" {
  type        = string
  description = "Logs EBS volume type for Database Server"
}

variable "orange_non_unnpi_db_logs_vol_size" {
  type        = string
  description = "Logs EBS volume size for Database Server"
}

variable "orange_non_unnpi_db_logs_vol_iops" {
  type        = string
  description = "Logs EBS volume IOPS for Database Server"
}

variable "orange_non_unnpi_db_logs_vol_throughput" {
  type        = string
  description = "Logs EBS volume throughput for Database Server"
}

# WBM Server Variables
variable "orange_non_unnpi_wbm_instance_ami" {
  description = "Instance AMI for WBM server"
  type        = string
}

variable "orange_non_unnpi_wbm_instance_type" {
  description = "Instance type for WBM server"
  type        = string
  default     = "m5.xlarge" # 4 vCPU, 16GB RAM
}

variable "orange_non_unnpi_wbm_os_vol_type" {
  description = "OS EBS volume type for WBM server"
  type        = string
  default     = "gp3"
}

variable "orange_non_unnpi_wbm_os_vol_size" {
  description = "OS EBS volume size for WBM server"
  type        = number
  default     = 128
}

variable "orange_non_unnpi_wbm_os_vol_iops" {
  description = "OS EBS volume IOPS for WBM server"
  type        = number
  default     = 3000
}

variable "orange_non_unnpi_wbm_os_vol_throughput" {
  description = "OS EBS volume throughput for WBM server"
  type        = number
  default     = 125
}

variable "orange_non_unnpi_wbm_data_vol_type" {
  description = "Data EBS volume type for WBM server"
  type        = string
  default     = "gp3"
}

variable "orange_non_unnpi_wbm_data_vol_size" {
  description = "Data EBS volume size for WBM server"
  type        = number
  default     = 372
}

variable "orange_non_unnpi_wbm_data_vol_iops" {
  description = "Data EBS volume IOPS for WBM server"
  type        = number
  default     = 3000
}

variable "orange_non_unnpi_wbm_data_vol_throughput" {
  description = "Data EBS volume throughput for WBM server"
  type        = number
  default     = 125
}

#########################
### Entra Connect Variables
#########################

variable "orange_non_unnpi_entra_instance_ami" {
  description = "Instance AMI for Entra Connect server"
  type        = string
}

variable "orange_non_unnpi_entra_instance_type" {
  description = "Instance type for Entra Connect server"
  type        = string
}

variable "orange_non_unnpi_entra_os_vol_type" {
  description = "OS EBS volume type for Entra Connect server"
  type        = string
}

variable "orange_non_unnpi_entra_os_vol_size" {
  description = "OS EBS volume size for Entra Connect server"
  type        = string
}

variable "orange_non_unnpi_entra_os_vol_iops" {
  description = "OS EBS volume iops for Entra Connect server"
  type        = string
}

variable "orange_non_unnpi_entra_os_vol_throughput" {
  description = "OS EBS volume throughput for Entra Connect server"
  type        = string
}

variable "orange_non_unnpi_entra_data_vol_type" {
  description = "Data EBS volume type for Entra Connect server"
  type        = string
}

variable "orange_non_unnpi_entra_data_vol_size" {
  description = "Data EBS volume size for Entra Connect server"
  type        = string
}

variable "orange_non_unnpi_entra_data_vol_iops" {
  description = "Data EBS volume iops for Entra Connect server"
  type        = string
}

variable "orange_non_unnpi_entra_data_vol_throughput" {
  description = "Data EBS volume throughput for Entra Connect server"
  type        = string
}
