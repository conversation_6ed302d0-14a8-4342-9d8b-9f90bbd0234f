#############
### NonUNNPI access roles
#############

## The role here is meant to be used by "app admins" for the All non-UNNPI envs
## It gives access to all the nonunnpi cloud components for viewing, and access to connect to nonunnpi ec2 instances via ssm
## Starting point is mbps here, so some sections commented out until/if we need them.
## eventually this will probably need to be a specific list of ec2 instances other nonunnpi objects
## This policy will be referenced by the sso instance created and managed by the nta, it can be used as a customer managed policy


locals {
  nonunnpi_federation_tags = {
    nnpi        = "false",
    impactLevel = "il4",
  }
  # IF you want to change the role names to a different friendly name, this would be where you make the change
  account_friendly_name = "eplm"
}

# Provide a policy to give minimum access to db and ec2 engineers
data "aws_iam_policy_document" "nonunnpi_cloudadmin" {

  # Cloud Trail, Cloud Watch
  # Prvide access to Cloud Trail and CloudWatch
  statement {
    sid    = "CloudWatchTrailAccess"
    effect = "Allow"
    actions = [
      "cloudWatch:DescribeAlarms",
      "cloudwatch:ListMetrics",
      "cloudtrail:DescribeTrails",
      "cloudtrail:ListEventDataStores",
      "cloudtrail:LookupEvents",
      "config:DescribeConfigurationRecorderStatus",
      "config:DescribeConfigurationRecorders",
      "logs:Describe*",
      "logs:ListAnomalies",
      "logs:ListLogAnomalyDetectors",
      "logs:ListLogDeliveries",
      "logs:ListTagsForResource",
      "logs:ListTagsLogGroup",
      "logs:FilterLogEvents",
      "logs:GetLogAnomalyDetector",
      "logs:GetLogEvents",
      "logs:GetLogDelivery",
      "logs:GetLogGroupFields",
      "logs:GetLogRecord",
      "logs:GetQueryResults",
      "logs:PutMetricFilter",
      "logs:DeleteMetricFilter",
      "logs:PutQueryDefinition",
      "logs:DeleteQueryDefinition",
      "logs:StartQuery",
      "logs:StartLiveTail",
      "logs:StopQuery",
      "logs:StopLiveTail",
      "logs:TestMetricFilter"
    ]
    resources = ["*"]
  }
  # SSM Access
  statement {
    sid    = "SSMAccess"
    effect = "Allow"
    actions = [
      "ssm:DescribeSessions",
      "ssm:DescribeInstanceProperties",
      "ssm:StartSession",
      "ssm:GetConnectionStatus",
      "ssm-guiconnect:GetConnection",
      "ssm-guiconnect:StartConnection",
      "ssm-guiconnect:CancelConnection",
      "ec2:DescribeAddresses",
      "ec2:DescribeImages",
      "ec2:DescribeInstanceInformation",
      "ec2:DescribeInstanceStatus",
      "ec2:DescribeInstances",
      "ec2:DescribeSecurityGroups",
      "ec2:DescribeSecurityGroupRules",
      "ec2:DescribeSnapshots",
      "ec2:DescribeVolumeStatus",
      "ec2:DescribeVolumes",
      "ssm:DescribeInstanceInformation",
      "sso:ListDirectoryAssociations",
      "identitystore:DescribeUser",
      "ssm:SendCommand",
      "ssm:GetCommandInvocation"
    ]
    resources = ["*"]
    # todo, modify to instances in the account
    #resources = [aws_instance.app_twx.arn, aws_instance.app_wc.arn]
  }
  #   statement {
  #     sid       = "SupportAccess"
  #     effect    = "Allow"
  #     resources = ["*"]

  #     actions = [
  #       "support:DescribeCommunications",
  #       "support:DescribeCommunication",
  #       "support:AddAttachmentsToSet",
  #       "support:AddCommunicationToCase",
  #       "support:SearchForCases",
  #       "support:ResolveCase",
  #       "support:DescribeCases",
  #       "support:DescribeIssueTypes",
  #       "support:DescribeSeverityLevels",
  #       "support:DescribeCaseAttributes",
  #     ]
  #   }
  statement {
    sid    = "SSMresume"
    effect = "Allow"
    actions = [
      "ssm:TerminateSession",
      "ssm:ResumeSession"
    ]
    resources = ["arn:aws-us-gov:ssm:*:*:session/$${aws:username}-*"]

  }


  #   # RDS Instance Access
  #   statement {
  #     sid    = "RDSAccess"
  #     effect = "Allow"
  #     actions = [
  #       "rds:DescribeDBInstances",
  #       "rds:DescribeDBLogFiles",
  #       "rds:DescribeDBParameterGroups",
  #       "rds:DescribeDBParameters",
  #       "rds:DescribeDBSecurityGroups",
  #       "rds:DescribeDBShardGroups",
  #       "rds:DescribeDBSnapshotAttributes",
  #       "rds:DescribeEngineDefaultParameters",
  #       "rds:DescribeEvents",
  #       "rds:RebootDBInstance",
  #       "rds:ListTagsForResource",
  #       "rds:ModifyDBInstance",
  #       "pi:DescribeDimensionKeys",
  #       "pi:GetResourceMetrics"
  #     ]
  #     resources = ["*"]

  #   }

  # VPC without condition
  statement {
    sid    = "VPC"
    effect = "Allow"
    actions = [
      "ec2:DescribeVpcs",
      "ec2:DescribeSubnets"
    ]
    resources = ["*"]

  }
  # VPC/Networking
  statement {
    sid    = "VPCandNetworking"
    effect = "Allow"
    actions = [
      "ec2:DescribeVpcAttribute",
      "ec2:DescribeAccountAttributes",
      "ec2:DescribeRouteTables",
      "ec2:DescribeDhcpOptions",
      "ec2:DescribeNetworkAcls"
    ]
    resources = ["*"]

  }
  #   statement {
  #     sid    = "r53access"
  #     effect = "Allow"
  #     actions = [
  #       "route53:ListReusableDelegationSets",
  #       "route53:GetHealthCheckLastFailureReason",
  #       "route53:GetHealthCheckStatus",
  #       "route53:GetChange",
  #       "route53:GetHostedZone",
  #       "route53:ListHostedZones",
  #       "route53:ListVPCAssociationAuthorizations",
  #       "route53:GetHealthCheck",
  #       "route53:GetReusableDelegationSetLimit",
  #       "route53:GetReusableDelegationSet",
  #       "route53:ListHostedZonesByName",
  #       "route53:ListTagsForResource",
  #       "route53:ListTagsForResources",
  #       "route53:GetAccountLimit",
  #       "route53:ListHostedZonesByVPC",
  #       "route53:GetCheckerIpRanges",
  #       "route53:ListCidrBlocks",
  #       "route53:ListHealthChecks",
  #       "route53:ListResourceRecordSets",
  #       "route53:GetHostedZoneLimit",
  #       "route53:ListCidrCollections",
  #       "route53:GetHostedZoneCount",
  #       "route53:ListCidrLocations",
  #       "route53:GetHealthCheckCount",
  #       "route53:ChangeResourceRecordSets"
  #     ]
  #     resources = ["*"]
  #   }

  #   statement {
  #     sid    = "SSMDocuments"
  #     effect = "Allow"
  #     actions = [
  #       "ssm:CancelCommand",
  #       "ssm:Create*",   # This is a catch all for all Create actions
  #       "ssm:Describe*", # This is a catch all for all Describe actions
  #       "ssm:Get*",      # This is a catch all for all Get actions
  #       "ssm:List*",     # This is a catch all for all List actions
  #       "ssm:SendCommand",
  #       "ssm:Start*",  # This is a catch all for all Start actions
  #       "ssm:Stop*",   # This is a catch all for all Stop actions
  #       "ssm:Update*", # This is a catch all for all Update actions
  #       "ssm:PutParameter"
  #       # Leaving out delete for now
  #     ]
  #     resources = ["*"]
  #   }

  #   ## a policy statement that denies access to ssm parameters with a specific path /tfvars/*
  #   statement {
  #     sid    = "DenySSMParameters"
  #     effect = "Deny"
  #     actions = [
  #       "ssm:Get*",
  #       "ssm:Create*",
  #       "ssm:Update*"
  #     ]
  #     resources = ["arn:aws-us-gov:ssm:*:*:parameter/tfvars/*"]
  #   }

  #   statement {
  #     sid       = "DenyCloudWatchAlerts"
  #     effect    = "Deny"
  #     resources = ["*"]
  #     actions = [
  #       "cloudwatch:PutDashboard",
  #       "cloudwatch:PutMetricData",
  #       "cloudwatch:DeleteAlarms",
  #       "cloudwatch:DeleteInsightRules",
  #       "cloudwatch:DeleteAnomalyDetector",
  #       "cloudwatch:CreateServiceLevelObjective",
  #       "cloudwatch:StartMetricStreams",
  #       "cloudwatch:DeleteDashboards",
  #       "cloudwatch:UntagResource",
  #       "cloudwatch:StopMetricStreams",
  #       "cloudwatch:DeleteMetricStream",
  #       "cloudwatch:SetAlarmState",
  #       "cloudwatch:PutAnomalyDetector",
  #       "cloudwatch:DeleteServiceLevelObjective",
  #       "cloudwatch:PutManagedInsightRules",
  #       "cloudwatch:EnableTopologyDiscovery",
  #       "cloudwatch:DisableInsightRules",
  #       "cloudwatch:EnableInsightRules",
  #       "cloudwatch:Link",
  #       "cloudwatch:PutCompositeAlarm",
  #       "cloudwatch:PutMetricStream",
  #       "cloudwatch:PutInsightRule",
  #       "cloudwatch:TagResource",
  #       "cloudwatch:PutMetricAlarm",
  #       "cloudwatch:UpdateServiceLevelObjective",
  #     ]
  #     condition {
  #       test     = "StringEquals"
  #       variable = "aws:ResourceTag/iacManaged"
  #       values   = ["true"]
  #     }
  #   }

  #   statement {
  #     sid       = "AllowNewCloudWatchAlerts"
  #     effect    = "Allow"
  #     resources = ["*"]
  #     actions = [
  #       "cloudwatch:*"
  #     ]
  #   }

  #   statement {
  #     sid       = "SNSTopicCreation"
  #     effect    = "Allow"
  #     resources = ["*"]

  #     actions = [
  #       "sns:Subscribe",
  #       "sns:ConfirmSubscription",
  #       "sns:Unsubscribe",
  #       "sns:Publish",
  #       "sns:Get*",
  #       "sns:List*",
  #     ]
  #   }
}

resource "aws_iam_policy" "nonunnpi_cloudadmin" {
  name        = "${local.account_friendly_name}-cloudadmin"
  description = "grant access to nonunnpi as an cloudadmin"
  policy      = data.aws_iam_policy_document.nonunnpi_cloudadmin.json
  tags = merge(local.nonunnpi_federation_tags, {
    Name = "${local.account_friendly_name}-cloudadmin"
  })

}


# # Provide a policy to give minimum access to db and ec2 engineers
# data "aws_iam_policy_document" "nonunnpi_cloudadmin2" {
#   # EC2/efs/appstream Access
#   statement {
#     sid    = "AllowListingInstances"
#     effect = "Allow"
#     actions = [
#       "appstream:CreateDirectoryConfig",
#       "appstream:CreateImageBuilder",
#       "appstream:CreateImageBuilderStreamingURL",
#       "appstream:CreateUpdatedImage",
#       "appstream:DescribeAppBlockBuilderAppBlockAssociations",
#       "appstream:DescribeAppBlocks",
#       "appstream:DescribeApplicationFleetAssociations",
#       "appstream:DescribeApplications",
#       "appstream:DescribeDirectoryConfigs",
#       "appstream:DescribeEntitlements",
#       "appstream:DescribeFleets",
#       "appstream:DescribeImageBuilders",
#       "appstream:DescribeImagePermissions",
#       "appstream:DescribeImages",
#       "appstream:DescribeSessions",
#       "appstream:DescribeStacks",
#       "appstream:DescribeUsageReportSubscriptions",
#       "appstream:DescribeUserStackAssociations",
#       "appstream:DescribeUsers",
#       "appstream:ListAssociatedFleets",
#       "appstream:ListAssociatedStacks",
#       "appstream:ListEntitledApplications",
#       "appstream:ListTagsForResource",
#       "appstream:StartFleet",
#       "appstream:StartImageBuilder",
#       "appstream:StopFleet",
#       "appstream:StopImageBuilder",
#       "appstream:UpdateApplication",
#       "appstream:UpdateDirectoryConfig",
#       "appstream:UpdateFleet",
#       "appstream:UpdateImagePermissions",
#       "appstream:DeleteImageBuilder",
#       "appstream:DeleteImage",
#       "ec2:DescribeAddresses",
#       "ec2:DescribeImages",
#       "ec2:DescribeInstanceInformation",
#       "ec2:DescribeInstanceStatus",
#       "ec2:DescribeInstances",
#       "ec2:DescribeSecurityGroups",
#       "ec2:DescribeSnapshots",
#       "ec2:DescribeVolumeStatus",
#       "ec2:DescribeVolumes",
#       "ec2:RebootInstances",
#       "ec2:StartInstances",
#       "ec2:StopInstances",
#       "elasticfilesystem:DescribeAccessPoints",
#       "elasticfilesystem:DescribeAccountPreferences",
#       "elasticfilesystem:DescribeBackupPolicy",
#       "elasticfilesystem:DescribeFileSystems",
#       "elasticfilesystem:DescribeFileSystemPolicy",
#       "elasticfilesystem:DescribeMountTargetSecurityGroups",
#       "elasticfilesystem:DescribeMountTargets",
#       "elasticfilesystem:DescribeReplicationConfigurations",
#       "elasticfilesystem:DescribeTags",
#       "elasticfilesystem:ListTagsForResource",
#       "elasticloadbalancing:DescribeAccountLimits",
#       "elasticloadbalancing:DescribeInstanceHealth",
#       "elasticloadbalancing:DescribeListenerCertificates",
#       "elasticloadbalancing:DescribeListeners",
#       "elasticloadbalancing:DescribeLoadBalancerAttributes",
#       "elasticloadbalancing:DescribeLoadBalancerPolicies",
#       "elasticloadbalancing:DescribeLoadBalancerPolicyTypes",
#       "elasticloadbalancing:DescribeLoadBalancers",
#       "elasticloadbalancing:DescribeRules",
#       "elasticloadbalancing:DescribeSSLPolicies",
#       "elasticloadbalancing:DescribeTargetGroupAttributes",
#       "elasticloadbalancing:DescribeTargetGroups",
#       "elasticloadbalancing:DescribeTargetHealth",
#       "elasticloadbalancing:DescribeTags",
#       "elasticloadbalancing:DescribeTrustStoreAssociations",
#       "elasticloadbalancing:DescribeTrustStoreRevocations",
#       "elasticloadbalancing:DescribeTrustStores",
#       "elasticloadbalancing:GetTrustStoreCaCertificatesBundle",
#       "elasticloadbalancing:GetTrustStoreRevocationContent",
#       "ds:CheckAlias",
#       "ds:DescribeCertificate",
#       "ds:DescribeClientAuthenticationSettings",
#       "ds:DescribeConditionalForwarders",
#       "ds:DescribeDirectories",
#       "ds:DescribeDomainControllers",
#       "ds:DescribeEventTopics",
#       "ds:DescribeLDAPSSettings",
#       "ds:DescribeLogSubscriptions",
#       "ds:DescribeRegions",
#       "ds:DescribeSettings",
#       "ds:DescribeSharedDirectories",
#       "ds:DescribeSnapshotLimits",
#       "ds:DescribeSnapshots",
#       "ds:GetAuthorizedApplicationDetails",
#       "ds:GetDirectoryLimits",
#       "ds:GetSnapshotLimits",
#       "ds:ListAuthorizedApplications",
#       "ds:ListCertificates",
#       "ds:ListIpRoutes",
#       "ds:ListLogSubscriptions",
#       "ds:ListSchemaExtensions",
#       "ds:ListTagsForResource",
#       "ds:VerifyTrust",
#       "ds:ResetUserPassword",
#       "fsx:DescribeAssociatedFileGateways",
#       "fsx:DescribeBackups",
#       "fsx:DescribeDataRepositoryAssociations",
#       "fsx:DescribeDataRepositoryTasks",
#       "fsx:DescribeFileCaches",
#       "fsx:DescribeFileSystemAliases",
#       "fsx:DescribeFileSystems",
#       "fsx:DescribeSharedVpcConfiguration",
#       "fsx:DescribeSnapshots",
#       "fsx:DescribeStorageVirtualMachines",
#       "fsx:DescribeTags",
#       "fsx:DescribeVolumes",
#       "fsx:ListTagsForResource"
#     ]

#     resources = ["*"]

#   }

#   # Read Secrets
#   statement {
#     sid    = "ReadSecrets"
#     effect = "Allow"
#     actions = [
#       "secretsmanager:GetSecretValue",
#       "secretsmanager:ListSecrets",
#       "secretsmanager:GetRandomPassword",
#       "secretsmanager:GetResourcePolicy",
#       "secretsmanager:GetSecretValue",
#       "secretsmanager:DescribeSecret",
#       "secretsmanager:ListSecretVersionIds",
#       "secretsmanager:RestoreSecret",
#       "secretsmanager:PutSecretValue",
#       "secretsmanager:UpdateSecret"
#     ]
#     resources = ["*"]

#   }

#   # KMS Access for secrets
#   statement {
#     sid    = "KMSAccesssecrets"
#     effect = "Allow"
#     actions = [
#       "kms:RetireGrant",
#       "kms:CreateGrant",
#       "kms:ReEncrypt*",
#       "kms:GenerateDataKey*",
#       "kms:Encrypt",
#       "kms:DescribeKey",
#       "kms:Decrypt"
#     ]
#     resources = ["*"]
#   }

#   # KMS Access for ssm
#   statement {
#     sid    = "KMSAccess"
#     effect = "Allow"
#     actions = [
#       "kms:RetireGrant",
#       "kms:CreateGrant",
#       "kms:ReEncrypt*",
#       "kms:GenerateDataKey*",
#       "kms:Encrypt",
#       "kms:DescribeKey",
#       "kms:Decrypt"
#     ]
#     resources = ["arn:aws-us-gov:kms:us-gov-west-1:************:key/mrk-015a042a6d4a4836bb916cb0262dfc53"]
#   }

#   # Read Security Groups
#   statement {
#     sid    = "ReadSecurityGroups"
#     effect = "Allow"
#     actions = [
#       "ec2:DescribeSecurityGroupReferences",
#       "ec2:DescribeSecurityGroupRules",
#       "ec2:DescribeSecurityGroups",
#       "ec2:DescribeStaleSecurityGroups"
#     ]
#     # need to add specific SGs
#     resources = ["*"]
#   }
#   # iam passrole for role/appstream in the account, allows modifying the fleet
#   statement {
#     sid    = "PassRoleforAppstream"
#     effect = "Allow"
#     actions = [
#       "iam:PassRole"
#     ]
#     resources = ["arn:aws-us-gov:iam::${data.aws_caller_identity.current.account_id}:role/appstream", "arn:aws-us-gov:iam::${data.aws_caller_identity.current.account_id}:role/appstream-mgmt-prod"]
#   }

#   # License Manager Admin Access
#   statement {
#     sid    = "LicenseManager"
#     effect = "Allow"
#     actions = [
#       "license-manager:*"
#     ]
#     resources = ["*"]
#   }

#   # Read Only IAM
#   statement {
#     sid    = "ReadOnlyIAM"
#     effect = "Allow"
#     actions = [
#       "iam:GetRole",
#       "iam:GetPolicyVersion",
#       "iam:ListRoleTags",
#       "iam:GetPolicy",
#       "iam:ListGroupPolicies",
#       "iam:ListEntitiesForPolicy",
#       "iam:ListUserPolicies",
#       "iam:GetGroup",
#       "iam:GetUserPolicy",
#       "iam:ListGroupsForUser",
#       "iam:ListAttachedRolePolicies",
#       "iam:ListAttachedUserPolicies",
#       "iam:ListAttachedGroupPolicies",
#       "iam:GetGroupPolicy",
#       "iam:ListPolicyTags",
#       "iam:GetUser",
#       "iam:ListRolePolicies",
#       "iam:GetRolePolicy",
#       "iam:ListUserTags",
#       "iam:ListRoles",
#       "iam:ListUsers",
#       "iam:ListGroups",
#       "iam:ListPolicies"
#     ]
#     resources = ["*"]

#   }

# }


# resource "aws_iam_policy" "nonunnpi_cloudadmin2" {
#   name        = "${local.account_friendly_name}-cloudadmin2"
#   description = "grant access to nonunnpi as an cloudadmin2"
#   policy      = data.aws_iam_policy_document.nonunnpi_cloudadmin2.json
#   tags = merge(local.nonunnpi_federation_tags, {
#     Name = "${local.account_friendly_name}-cloudadmin2"
#   })

# }


# # resource "aws_iam_role" "nonunnpi_cloudadmin" {
# #   name               = "${local.account_friendly_name}-paa-cloud-admin"
# #   assume_role_policy = data.aws_iam_policy_document.nonunnpi_federated_trust.json
# #   tags = merge(local.nonunnpi_federation_tags, {
# #     Name = "${local.account_friendly_name}-paa-cloud-admin"
# #   })
# # }

# # resource "aws_iam_role_policy_attachment" "nonunnpi_cloudadmin" {
# #   role       = aws_iam_role.nonunnpi_cloudadmin.name
# #   policy_arn = aws_iam_policy.nonunnpi_cloudadmin.arn
# # }

# # resource "aws_iam_role_policy_attachment" "nonunnpi_cloudadmin2" {
# #   role       = aws_iam_role.nonunnpi_cloudadmin.name
# #   policy_arn = aws_iam_policy.nonunnpi_cloudadmin2.arn
# # }

# # resource "aws_iam_role_policy_attachment" "nonunnpi_cloudadmin_s3" {
# #   role       = aws_iam_role.nonunnpi_cloudadmin.name
# #   policy_arn = aws_iam_policy.installfiles_admin.arn
# # }


# # resource "aws_iam_role_policy_attachment" "nonunnpi_cloudadmin_s3_build" {
# #   role       = aws_iam_role.nonunnpi_cloudadmin.name
# #   policy_arn = aws_iam_policy.wcbuilds_admin.arn
# # }

# # resource "aws_iam_role_policy_attachment" "nonunnpi_cloudadmin_s3_rehost" {
# #   role       = aws_iam_role.nonunnpi_cloudadmin.name
# #   policy_arn = aws_iam_policy.wcrehost_admin.arn
# # }


# ### Entra federation
# data "aws_iam_policy_document" "entra_federated_trust" {
#   statement {
#     principals {
#       type        = "Federated"
#       identifiers = ["arn:aws-us-gov:iam::${data.aws_caller_identity.current.account_id}:saml-provider/nmmes-entra"]
#     }
#     actions = ["sts:AssumeRoleWithSAML"]
#     condition {
#       test     = "StringEquals"
#       variable = "SAML:aud"
#       values   = ["https://signin.amazonaws-us-gov.com/saml"]
#     }
#     effect = "Allow"
#   }
# }


# resource "aws_iam_role" "entra_cloudadmin" {
#   name               = "${local.account_friendly_name}-paa-cld-admin"
#   assume_role_policy = data.aws_iam_policy_document.entra_federated_trust.json
#   tags = merge(local.nonunnpi_federation_tags, {
#     Name = "${local.account_friendly_name}-paa-cloud-admin"
#   })
# }

# resource "aws_iam_role_policy_attachment" "entra_cloudadmin" {
#   role       = aws_iam_role.entra_cloudadmin.name
#   policy_arn = aws_iam_policy.nonunnpi_cloudadmin.arn
# }

# resource "aws_iam_role_policy_attachment" "entra_cloudadmin2" {
#   role       = aws_iam_role.entra_cloudadmin.name
#   policy_arn = aws_iam_policy.nonunnpi_cloudadmin2.arn
# }

# resource "aws_iam_role_policy_attachment" "entra_cloudadmin_s3" {
#   role       = aws_iam_role.entra_cloudadmin.name
#   policy_arn = aws_iam_policy.installfiles_admin.arn
# }


# resource "aws_iam_role_policy_attachment" "entra_cloudadmin_s3_build" {
#   role       = aws_iam_role.entra_cloudadmin.name
#   policy_arn = aws_iam_policy.wcbuilds_admin.arn
# }

# resource "aws_iam_role_policy_attachment" "entra_cloudadmin_s3_rehost" {
#   role       = aws_iam_role.entra_cloudadmin.name
#   policy_arn = aws_iam_policy.wcrehost_admin.arn
# }

# resource "aws_iam_role_policy_attachment" "entra_cloudadmin_s3_content_dev1" {
#   role       = aws_iam_role.entra_cloudadmin.name
#   policy_arn = aws_iam_policy.wc_content_admin.arn
# }

# resource "aws_iam_role_policy_attachment" "entra_cloudadmin_s3_content_mig1" {
#   role       = aws_iam_role.entra_cloudadmin.name
#   policy_arn = aws_iam_policy.wc_content_mig1_admin.arn
# }

# resource "aws_iam_role_policy_attachment" "entra_cloudadmin_s3_content_preprod" {
#   role       = aws_iam_role.entra_cloudadmin.name
#   policy_arn = aws_iam_policy.wc_content_preprod_admin.arn
# }

# resource "aws_iam_role_policy_attachment" "entra_cloudadmin_s3_content_test" {
#   role       = aws_iam_role.entra_cloudadmin.name
#   policy_arn = aws_iam_policy.wc_content_test_admin.arn
# }

# resource "aws_iam_role_policy_attachment" "entra_cloudadmin_s3_content_prod" {
#   role       = aws_iam_role.entra_cloudadmin.name
#   policy_arn = aws_iam_policy.wc_content_prod_admin.arn
# }

# ### non-paa-user role
# # Grants access to view ec2 instance information, r/w to "nonunnpi_secret" secrets, and admin permissions to installfiles s3 bucket
# resource "aws_iam_role" "non_paa_user" {
#   name               = "${local.account_friendly_name}-non-paa-user"
#   assume_role_policy = data.aws_iam_policy_document.entra_federated_trust.json
#   tags = merge(local.nonunnpi_federation_tags, {
#     Name = "${local.account_friendly_name}-non-paa-user"
#   })
# }

# resource "aws_iam_policy" "non_paa_user_ec2" {
#   name        = "${local.account_friendly_name}-non-paa-user-ec2"
#   description = "grant access to view ec2 instance information as the non-paa-user role"
#   policy      = data.aws_iam_policy_document.non_paa_user_ec2.json
#   tags = merge(local.nonunnpi_federation_tags, {
#     Name = "${local.account_friendly_name}-non-paa-user-ec2"
#   })

# }

# # Provide a policy to give view access to ec2 non-paa-user role
# data "aws_iam_policy_document" "non_paa_user_ec2" {
#   statement {
#     sid    = "ec2"
#     effect = "Allow"
#     actions = [
#       "ec2:Describe*"
#     ]
#     resources = ["*"]
#   }

#   statement {
#     sid    = "elb"
#     effect = "Allow"
#     actions = [
#       "elasticloadbalancing:Describe*"
#     ]
#     resources = ["*"]
#   }

#   statement {
#     sid    = "CloudWatchMetrics"
#     effect = "Allow"
#     actions = [
#       "cloudwatch:ListMetrics",
#       "cloudwatch:GetMetricStatistics",
#       "cloudwatch:Describe*",
#       "cloudwatch:GetMetricData",
#       "cloudwatch:GetDashboard",
#       "cloudwatch:ListDashboards"
#     ]
#     resources = ["*"]
#   }

#   statement {
#     sid    = "Logs"
#     effect = "Allow"
#     actions = [
#       "logs:Describe*"
#     ]
#     resources = ["*"]
#   }

#   statement {
#     sid       = "SupportAccess"
#     effect    = "Allow"
#     resources = ["*"]

#     actions = [
#       "support:DescribeCommunications",
#       "support:DescribeCommunication",
#       "support:AddAttachmentsToSet",
#       "support:AddCommunicationToCase",
#       "support:SearchForCases",
#       "support:ResolveCase",
#       "support:DescribeCases",
#       "support:DescribeServices",
#       "support:DescribeIssueTypes",
#       "support:DescribeSeverityLevels",
#       "support:DescribeCaseAttributes",
#     ]
#   }

#   statement {
#     sid    = "LogsGroups"
#     effect = "Allow"
#     actions = [

#       "logs:GetLogRecord",
#       "logs:GetLogEvents",
#       "logs:StartLiveTail",
#       "logs:FilterLogEvents",
#       "logs:DescribeQueries",
#       "logs:GetQueryResults",
#       "logs:StartQuery",
#       "logs:StopQuery",
#       "logs:GetLogGroupFields"
#     ]
#     resources = [
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/mig1/eq/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/mig1/apache/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/mig1/windchill/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/mig1/pingfed/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/mig1/thingworx/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/preprod/eq/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/preprod/apache/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/preprod/windchill/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/preprod/pingfed/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/preprod/thingworx/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/dev1/eq/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/dev1/apache/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/dev1/windchill/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/dev1/pingfed/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/dev1/thingworx/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/preprod/eq/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/preprod/apache/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/preprod/windchill/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/preprod/pingfed/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/preprod/thingworx/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/test/eq/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/test/apache/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/test/windchill/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/test/pingfed/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/test/thingworx/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/prod/eq/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/prod/apache/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/prod/windchill/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/prod/pingfed/*",
#       "arn:aws-us-gov:logs:us-gov-west-1:${data.aws_caller_identity.current.account_id}:log-group:/prod/thingworx/*"
#     ]
#   }

#   statement {
#     sid    = "Autoscaling"
#     effect = "Allow"
#     actions = [
#       "autoscaling:Describe*"
#     ]
#     resources = ["*"]
#   }

#   # RDS Instance Access
#   statement {
#     sid    = "RDSAccess"
#     effect = "Allow"
#     actions = [
#       "rds:DescribeDBInstances",
#       "rds:DescribeDBLogFiles",
#       "rds:DescribeDBParameterGroups",
#       "rds:DescribeDBParameters",
#       "rds:DescribeDBSecurityGroups",
#       "rds:DescribeDBShardGroups",
#       "rds:DescribeDBSnapshotAttributes",
#       "rds:DescribeEngineDefaultParameters",
#       "rds:DescribeEvents",
#       "rds:ListTagsForResource",
#       "pi:DescribeDimensionKeys",
#       "pi:GetResourceMetrics",
#       "rds:DescribeDBClusters",
#       "rds:DescribeGlobalClusters"
#     ]
#     resources = ["*"]
#   }

# }

# resource "aws_iam_policy" "non_paa_user_secrets" {
#   name        = "${local.account_friendly_name}-non-paa-user-secrets"
#   description = "grant access to mbps shared secrets as the non-paa-user role"
#   policy      = data.aws_iam_policy_document.non_paa_user_secrets.json
#   tags = merge(local.nonunnpi_federation_tags, {
#     Name = "${local.account_friendly_name}-non-paa-user-secrets"
#   })
# }

# #Provide a policy to give r/w access to mbps secrets to the non-paa-user role
# data "aws_iam_policy_document" "non_paa_user_secrets" {
#   statement {
#     sid    = "SecretsManager"
#     effect = "Allow"
#     actions = [
#       "secretsmanager:GetResourcePolicy",
#       "secretsmanager:GetSecretValue",
#       "secretsmanager:DescribeSecret",
#       "secretsmanager:ListSecretVersionIds",
#       "secretsmanager:PutSecretValue",
#       "secretsmanager:RestoreSecret",
#       "secretsmanager:UpdateSecret"
#     ]
#     resources = [
#       aws_secretsmanager_secret.nonunnpi_secret["dev1"].arn,
#       aws_secretsmanager_secret.nonunnpi_secret["mig1"].arn,
#       aws_secretsmanager_secret.nonunnpi_secret["preprod"].arn,
#       aws_secretsmanager_secret.nonunnpi_secret["mgmt-dev"].arn,
#       aws_secretsmanager_secret.nonunnpi_secret["mgmt-prod"].arn,
#       aws_secretsmanager_secret.nonunnpi_secret["test"].arn,
#       aws_secretsmanager_secret.nonunnpi_secret["prod"].arn
#     ]
#   }

#   statement {
#     sid    = "ListSecrets"
#     effect = "Allow"
#     actions = [
#       "secretsmanager:ListSecrets"
#     ]
#     resources = ["*"]
#   }


#   statement {
#     sid    = "KMSAccesssecrets"
#     effect = "Allow"
#     actions = [
#       "kms:RetireGrant",
#       "kms:CreateGrant",
#       "kms:ReEncrypt*",
#       "kms:GenerateDataKey*",
#       "kms:Encrypt",
#       "kms:DescribeKey",
#       "kms:Decrypt"
#     ]
#     resources = [
#       aws_kms_key.nonunnpi_secret["dev1"].arn,
#       aws_kms_key.nonunnpi_secret["mig1"].arn,
#       aws_kms_key.nonunnpi_secret["preprod"].arn,
#       aws_kms_key.nonunnpi_secret["mgmt-dev"].arn,
#       aws_kms_key.nonunnpi_secret["mgmt-prod"].arn,
#       aws_kms_key.nonunnpi_secret["prod"].arn,
#       aws_kms_key.nonunnpi_secret["test"].arn
#     ]
#   }
# }

# resource "aws_iam_role_policy_attachment" "non_paa_user_ec2" {
#   role       = aws_iam_role.non_paa_user.name
#   policy_arn = aws_iam_policy.non_paa_user_ec2.arn
# }

# resource "aws_iam_role_policy_attachment" "non_paa_user_secrets" {
#   role       = aws_iam_role.non_paa_user.name
#   policy_arn = aws_iam_policy.non_paa_user_secrets.arn
# }

# resource "aws_iam_role_policy_attachment" "non_paa_user_s3_installfiles" {
#   role       = aws_iam_role.non_paa_user.name
#   policy_arn = aws_iam_policy.installfiles_admin.arn
# }

# resource "aws_iam_role_policy_attachment" "non_paa_user_s3_wcbuilds" {
#   role       = aws_iam_role.non_paa_user.name
#   policy_arn = aws_iam_policy.wcbuilds_admin.arn
# }

# resource "aws_iam_role_policy_attachment" "non_paa_user_s3_wcrehost" {
#   role       = aws_iam_role.non_paa_user.name
#   policy_arn = aws_iam_policy.wcrehost_admin.arn
# }

# resource "aws_iam_role_policy_attachment" "wc_content_admin" {
#   role       = aws_iam_role.non_paa_user.name
#   policy_arn = aws_iam_policy.wc_content_admin.arn
# }

# resource "aws_iam_role_policy_attachment" "wc_content_mig1_admin" {
#   role       = aws_iam_role.non_paa_user.name
#   policy_arn = aws_iam_policy.wc_content_mig1_admin.arn
# }

# resource "aws_iam_role_policy_attachment" "wc_content_test_admin" {
#   role       = aws_iam_role.non_paa_user.name
#   policy_arn = aws_iam_policy.wc_content_test_admin.arn
# }

# resource "aws_iam_role_policy_attachment" "wc_content_preprod_admin" {
#   role       = aws_iam_role.non_paa_user.name
#   policy_arn = aws_iam_policy.wc_content_preprod_admin.arn
# }

# resource "aws_iam_role_policy_attachment" "wc_content_prod_admin" {
#   role       = aws_iam_role.non_paa_user.name
#   policy_arn = aws_iam_policy.wc_content_prod_admin.arn
# }